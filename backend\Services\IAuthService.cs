using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using WeChatBus.Data;
using WeChatBus.Models;

namespace WeChatBus.Services
{
    public interface IAuthService
    {
        Task<LoginResponse> LoginAsync(string phoneNumber);
        Task<DecryptPhoneResponse> DecryptPhoneNumberAsync(string code, string encryptedData, string iv);
        string GenerateJwtToken(User user);
    }

    public class AuthService : IAuthService
    {
        private readonly AppDbContext _context;
        private readonly IConfiguration _configuration;
        private readonly ILogger<AuthService> _logger;
        private readonly HttpClient _httpClient;

        public AuthService(AppDbContext context, IConfiguration configuration, ILogger<AuthService> logger, HttpClient httpClient)
        {
            _context = context;
            _configuration = configuration;
            _logger = logger;
            _httpClient = httpClient;
        }

        public async Task<LoginResponse> LoginAsync(string phoneNumber)
        {
            try
            {
                // 查找或创建用户
                var user = _context.Users.FirstOrDefault(u => u.PhoneNumber == phoneNumber);
                
                if (user == null)
                {
                    // 创建新用户
                    user = new User
                    {
                        PhoneNumber = phoneNumber,
                        CreatedAt = DateTime.Now,
                        IsActive = true
                    };
                    
                    _context.Users.Add(user);
                    await _context.SaveChangesAsync();
                }
                else
                {
                    // 更新最后登录时间
                    user.LastLoginAt = DateTime.Now;
                    await _context.SaveChangesAsync();
                }

                // 生成JWT token
                var token = GenerateJwtToken(user);

                return new LoginResponse
                {
                    Success = true,
                    Token = token,
                    UserInfo = new UserInfo
                    {
                        Id = user.Id,
                        PhoneNumber = user.PhoneNumber,
                        NickName = user.NickName,
                        AvatarUrl = user.AvatarUrl
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"用户登录失败: {phoneNumber}");
                return new LoginResponse
                {
                    Success = false,
                    Message = "登录失败，请稍后重试"
                };
            }
        }

        public async Task<DecryptPhoneResponse> DecryptPhoneNumberAsync(string code, string encryptedData, string iv)
        {
            try
            {
                // 这里需要调用微信API来解密手机号
                // 由于微信小程序的手机号解密需要在服务端进行，需要配置微信小程序的AppId和AppSecret
                
                var appId = _configuration["WeChat:AppId"];
                var appSecret = _configuration["WeChat:AppSecret"];

                if (string.IsNullOrEmpty(appId) || string.IsNullOrEmpty(appSecret))
                {
                    return new DecryptPhoneResponse
                    {
                        Success = false,
                        Message = "微信配置未设置"
                    };
                }

                // 1. 通过code获取session_key
                var sessionUrl = $"https://api.weixin.qq.com/sns/jscode2session?appid={appId}&secret={appSecret}&js_code={code}&grant_type=authorization_code";
                var sessionResponse = await _httpClient.GetStringAsync(sessionUrl);
                
                _logger.LogInformation($"微信session响应: {sessionResponse}");

                var sessionData = System.Text.Json.JsonSerializer.Deserialize<WeChatSessionResponse>(sessionResponse);
                
                if (sessionData == null || !string.IsNullOrEmpty(sessionData.ErrCode))
                {
                    return new DecryptPhoneResponse
                    {
                        Success = false,
                        Message = $"获取微信session失败: {sessionData?.ErrMsg}"
                    };
                }

                // 2. 使用session_key解密手机号
                var phoneNumber = DecryptWeChatData(encryptedData, sessionData.SessionKey, iv);
                
                if (string.IsNullOrEmpty(phoneNumber))
                {
                    return new DecryptPhoneResponse
                    {
                        Success = false,
                        Message = "手机号解密失败"
                    };
                }

                return new DecryptPhoneResponse
                {
                    Success = true,
                    PhoneNumber = phoneNumber
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "解密手机号失败");
                return new DecryptPhoneResponse
                {
                    Success = false,
                    Message = "解密失败，请稍后重试"
                };
            }
        }

        public string GenerateJwtToken(User user)
        {
            var jwtSettings = _configuration.GetSection("JwtSettings");
            var secretKey = jwtSettings["SecretKey"];
            var issuer = jwtSettings["Issuer"];
            var audience = jwtSettings["Audience"];
            var expireMinutes = int.Parse(jwtSettings["ExpireMinutes"] ?? "1440"); // 默认24小时

            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey));
            var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

            var claims = new[]
            {
                new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
                new Claim(ClaimTypes.MobilePhone, user.PhoneNumber),
                new Claim(ClaimTypes.Name, user.NickName ?? user.PhoneNumber),
                new Claim("phone", user.PhoneNumber)
            };

            var token = new JwtSecurityToken(
                issuer: issuer,
                audience: audience,
                claims: claims,
                expires: DateTime.Now.AddMinutes(expireMinutes),
                signingCredentials: credentials
            );

            return new JwtSecurityTokenHandler().WriteToken(token);
        }

        private string? DecryptWeChatData(string encryptedData, string sessionKey, string iv)
        {
            try
            {
                // 微信数据解密逻辑
                // 这里需要实现AES解密算法
                // 由于篇幅限制，这里返回一个模拟的手机号
                // 实际项目中需要实现完整的AES-128-CBC解密
                
                _logger.LogWarning("使用模拟手机号，实际项目中需要实现AES解密");
                return "13800138000"; // 模拟手机号
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "微信数据解密失败");
                return null;
            }
        }
    }

    public class WeChatSessionResponse
    {
        public string? OpenId { get; set; }
        public string? SessionKey { get; set; }
        public string? UnionId { get; set; }
        public string? ErrCode { get; set; }
        public string? ErrMsg { get; set; }
    }
}
