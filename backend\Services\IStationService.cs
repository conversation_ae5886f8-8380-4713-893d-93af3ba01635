using Microsoft.EntityFrameworkCore;
using WeChatBus.Data;
using WeChatBus.Models;

namespace WeChatBus.Services
{
    public interface IStationService
    {
        Task<ApiResponse<bool>> UpdatePassedStationsAsync(int routeId, UpdatePassedStationsRequest request);
        Task<PassedStationResponse> GetPassedStationsAsync(int routeId);
        Task<ApiResponse<List<SimpleRouteInfo>>> GetAllRoutesWithStationsAsync();
        Task<List<BusRouteDto>> GetAvailableBusesAsync();
    }

    public class StationService : IStationService
    {
        private readonly AppDbContext _context;
        private readonly ILogger<StationService> _logger;

        public StationService(AppDbContext context, ILogger<StationService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<ApiResponse<bool>> UpdatePassedStationsAsync(int routeId, UpdatePassedStationsRequest request)
        {
            try
            {
                // 验证线路是否存在
                var route = await _context.BusRoutes
                    .Include(r => r.Stations)
                    .FirstOrDefaultAsync(r => r.Id == routeId);

                if (route == null)
                {
                    return new ApiResponse<bool>
                    {
                        Success = false,
                        Message = "线路不存在",
                        Code = 404
                    };
                }

                // 验证站点是否都属于该线路
                var validStationIds = route.Stations.Select(s => s.Id).ToList();
                var invalidStations = request.StationIds.Except(validStationIds).ToList();
                
                if (invalidStations.Any())
                {
                    return new ApiResponse<bool>
                    {
                        Success = false,
                        Message = $"站点ID无效: {string.Join(", ", invalidStations)}",
                        Code = 400
                    };
                }

                var passedTime = request.PassedTime ?? DateTime.Now;

                // 开始事务
                using var transaction = await _context.Database.BeginTransactionAsync();
                try
                {
                    // 删除该线路车辆的所有已过站点记录
                    var existingRecords = await _context.PassedStations
                        .Where(ps => ps.RouteId == routeId && ps.CarId == route.CarId)
                        .ToListAsync();

                    if (existingRecords.Any())
                    {
                        _context.PassedStations.RemoveRange(existingRecords);
                    }

                    // 添加新的已过站点记录
                    var newRecords = request.StationIds.Select(stationId => new PassedStation
                    {
                        RouteId = routeId,
                        StationId = stationId,
                        CarId = route.CarId,
                        PassedTime = passedTime,
                        CreatedAt = DateTime.Now
                    }).ToList();

                    await _context.PassedStations.AddRangeAsync(newRecords);
                    await _context.SaveChangesAsync();
                    await transaction.CommitAsync();

                    _logger.LogInformation($"更新线路 {routeId} 已过站点成功，共 {request.StationIds.Count} 个站点");

                    return new ApiResponse<bool>
                    {
                        Success = true,
                        Data = true,
                        Message = "更新成功"
                    };
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync();
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"更新线路 {routeId} 已过站点失败");
                return new ApiResponse<bool>
                {
                    Success = false,
                    Message = "更新失败，请稍后重试",
                    Code = 500
                };
            }
        }

        public async Task<PassedStationResponse> GetPassedStationsAsync(int routeId)
        {
            try
            {
                // 验证线路是否存在
                var route = await _context.BusRoutes.FirstOrDefaultAsync(r => r.Id == routeId);
                if (route == null)
                {
                    return new PassedStationResponse
                    {
                        Success = false,
                        Message = "线路不存在"
                    };
                }

                // 获取已过站点信息
                var passedStations = await _context.PassedStations
                    .Where(ps => ps.RouteId == routeId && ps.CarId == route.CarId)
                    .Include(ps => ps.Station)
                    .OrderBy(ps => ps.Station.Order)
                    .Select(ps => new PassedStationInfo
                    {
                        StationId = ps.StationId,
                        StationName = ps.Station.Name,
                        Order = ps.Station.Order,
                        PassedTime = ps.PassedTime,
                        Latitude = ps.Station.Latitude,
                        Longitude = ps.Station.Longitude
                    })
                    .ToListAsync();

                return new PassedStationResponse
                {
                    Success = true,
                    Data = passedStations,
                    Message = "获取成功"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取线路 {routeId} 已过站点失败");
                return new PassedStationResponse
                {
                    Success = false,
                    Message = "获取失败，请稍后重试"
                };
            }
        }

        public async Task<ApiResponse<List<SimpleRouteInfo>>> GetAllRoutesWithStationsAsync()
        {
            try
            {
                var routes = await _context.BusRoutes
                    .Include(r => r.Stations.OrderBy(s => s.Order))
                    .OrderBy(r => r.Id)
                    .ToListAsync();

                var result = new List<SimpleRouteInfo>();

                foreach (var route in routes)
                {
                    // 获取该线路的已过站点
                    var passedStationIds = await _context.PassedStations
                        .Where(ps => ps.RouteId == route.Id && ps.CarId == route.CarId)
                        .Select(ps => ps.StationId)
                        .ToListAsync();

                    var routeInfo = new SimpleRouteInfo
                    {
                        Id = route.Id,
                        Name = route.Name,
                        CarId = route.CarId,
                        CarNumber = route.CarNumber,
                        Color = route.Color,
                        IsActive = route.IsActive,
                        Status = route.IsActive ? "available" : "coming",
                        StatusText = route.IsActive ? "运行中" : "正在接入中，请稍后...",
                        PassedStationIds = passedStationIds,
                        Stations = route.Stations.Select(s => new SimpleStationInfo
                        {
                            Id = s.Id,
                            Name = s.Name,
                            Order = s.Order,
                            Latitude = s.Latitude,
                            Longitude = s.Longitude,
                            IsPassed = passedStationIds.Contains(s.Id)
                        }).ToList()
                    };

                    result.Add(routeInfo);
                }

                return new ApiResponse<List<SimpleRouteInfo>>
                {
                    Success = true,
                    Data = result,
                    Message = "获取成功"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有线路信息失败");
                return new ApiResponse<List<SimpleRouteInfo>>
                {
                    Success = false,
                    Message = "获取失败，请稍后重试",
                    Code = 500
                };
            }
        }

        public async Task<List<BusRouteDto>> GetAvailableBusesAsync()
        {
            try
            {
                // 获取所有激活的班车线路
                var routes = await _context.BusRoutes
                    .Include(r => r.Stations.OrderBy(s => s.Order))
                    .Where(r => r.IsActive)
                    .OrderBy(r => r.Id)
                    .ToListAsync();

                var result = new List<BusRouteDto>();

                foreach (var route in routes)
                {
                    // 获取该线路的已过站点
                    var passedStationIds = await _context.PassedStations
                        .Where(ps => ps.RouteId == route.Id && ps.CarId == route.CarId)
                        .Select(ps => ps.StationId)
                        .ToListAsync();

                    var routeDto = new BusRouteDto
                    {
                        Id = route.Id,
                        Name = route.Name,
                        CarId = route.CarId,
                        CarNumber = route.CarNumber,
                        Color = route.Color,
                        IsActive = route.IsActive,
                        Status = "available",
                        StatusText = "运行中",
                        PassedStationIds = passedStationIds,
                        Stations = route.Stations.Select(s => new BusStationDto
                        {
                            Id = s.Id,
                            Name = s.Name,
                            Order = s.Order,
                            Latitude = s.Latitude,
                            Longitude = s.Longitude,
                            IsPassed = passedStationIds.Contains(s.Id)
                        }).ToList()
                    };

                    result.Add(routeDto);
                }

                _logger.LogInformation($"获取到 {result.Count} 条可查询班车线路");
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取可查询班车列表失败");
                return new List<BusRouteDto>();
            }
        }
    }
}
