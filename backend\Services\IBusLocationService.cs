using Microsoft.EntityFrameworkCore;
using WeChatBus.Data;
using WeChatBus.Models;

namespace WeChatBus.Services
{
    public interface IBusLocationService
    {
        Task<BusLocationResponse> GetBusLocationAsync(string carId);
        Task<RouteInfoResponse> GetRouteInfoAsync(int routeId);
        Task<List<BusRoute>> GetAllRoutesAsync();
    }

    public class BusLocationService : IBusLocationService
    {
        private readonly AppDbContext _context;
        private readonly IGpsService _gpsService;
        private readonly ILogger<BusLocationService> _logger;

        public BusLocationService(AppDbContext context, IGpsService gpsService, ILogger<BusLocationService> logger)
        {
            _context = context;
            _gpsService = gpsService;
            _logger = logger;
        }

        public async Task<BusLocationResponse> GetBusLocationAsync(string carId)
        {
            try
            {
                // 从GPS服务获取车辆位置
                var locations = await _gpsService.GetVehicleLocationsAsync(new[] { carId });
                var vehicleData = locations.FirstOrDefault(v => v.CarId == carId);

                if (vehicleData == null)
                {
                    return new BusLocationResponse
                    {
                        Success = false,
                        Message = "未找到车辆位置信息"
                    };
                }

                // 获取对应的线路信息
                var route = await _context.BusRoutes
                    .Include(r => r.Stations.OrderBy(s => s.Order))
                    .FirstOrDefaultAsync(r => r.CarId == carId);

                var locationData = new BusLocationData
                {
                    CarId = vehicleData.CarId,
                    Lat = vehicleData.Lat,
                    Lng = vehicleData.Lng,
                    Addr = vehicleData.Addr,
                    Speed = vehicleData.Speed,
                    Time = vehicleData.Time,
                    State = vehicleData.State
                };

                // 如果有线路信息，计算当前位置状态
                if (route != null && route.Stations.Any())
                {
                    var stationInfos = route.Stations.Select(s => new StationInfo
                    {
                        Id = s.Id,
                        Name = s.Name,
                        Order = s.Order,
                        Latitude = s.Latitude,
                        Longitude = s.Longitude
                    }).ToList();

                    var currentStationIndex = CalculateNearestStation(vehicleData.Lat, vehicleData.Lng, stationInfos);
                    var nearestStation = stationInfos[currentStationIndex];
                    var distanceToStation = CalculateDistance(vehicleData.Lat, vehicleData.Lng,
                        nearestStation.Latitude, nearestStation.Longitude);

                    // 判断是否在站点附近（30米以内认为到站）
                    var isAtStation = distanceToStation <= 30;

                    locationData.CurrentStationIndex = currentStationIndex;
                    locationData.NearestStationName = nearestStation.Name;
                    locationData.DistanceToStation = Math.Round(distanceToStation, 1);
                    locationData.IsAtStation = isAtStation;
                    locationData.LocationStatus = isAtStation ? "在站" : "行驶中";
                }

                return new BusLocationResponse
                {
                    Success = true,
                    Data = locationData
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取车辆位置失败: {carId}");
                return new BusLocationResponse
                {
                    Success = false,
                    Message = "获取位置信息失败"
                };
            }
        }

        public async Task<RouteInfoResponse> GetRouteInfoAsync(int routeId)
        {
            try
            {
                var route = await _context.BusRoutes
                    .Include(r => r.Stations.OrderBy(s => s.Order))
                    .FirstOrDefaultAsync(r => r.Id == routeId);

                if (route == null)
                {
                    return new RouteInfoResponse
                    {
                        Success = false,
                        Message = "未找到线路信息"
                    };
                }

                return new RouteInfoResponse
                {
                    Success = true,
                    Data = new RouteInfo
                    {
                        Id = route.Id,
                        Name = route.Name,
                        CarNumber = route.CarNumber,
                        Color = route.Color,
                        Stations = route.Stations.Select(s => new StationInfo
                        {
                            Id = s.Id,
                            Name = s.Name,
                            Order = s.Order,
                            Latitude = s.Latitude,
                            Longitude = s.Longitude
                        }).ToList()
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取线路信息失败: {routeId}");
                return new RouteInfoResponse
                {
                    Success = false,
                    Message = "获取线路信息失败"
                };
            }
        }

        public async Task<List<BusRoute>> GetAllRoutesAsync()
        {
            try
            {
                return await _context.BusRoutes
                    .OrderBy(r => r.Id)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有线路失败");
                return new List<BusRoute>();
            }
        }

        /// <summary>
        /// 计算两个GPS坐标之间的距离（米）
        /// </summary>
        public static double CalculateDistance(double lat1, double lng1, double lat2, double lng2)
        {
            const double R = 6371000; // 地球半径（米）
            
            var lat1Rad = lat1 * Math.PI / 180;
            var lat2Rad = lat2 * Math.PI / 180;
            var deltaLatRad = (lat2 - lat1) * Math.PI / 180;
            var deltaLngRad = (lng2 - lng1) * Math.PI / 180;

            var a = Math.Sin(deltaLatRad / 2) * Math.Sin(deltaLatRad / 2) +
                    Math.Cos(lat1Rad) * Math.Cos(lat2Rad) *
                    Math.Sin(deltaLngRad / 2) * Math.Sin(deltaLngRad / 2);
            
            var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
            
            return R * c;
        }

        /// <summary>
        /// 根据GPS坐标计算车辆当前最接近的站点
        /// </summary>
        public static int CalculateNearestStation(double busLat, double busLng, List<StationInfo> stations)
        {
            if (!stations.Any()) return 0;

            var minDistance = double.MaxValue;
            var nearestStationIndex = 0;

            for (int i = 0; i < stations.Count; i++)
            {
                var distance = CalculateDistance(busLat, busLng, stations[i].Latitude, stations[i].Longitude);
                if (distance < minDistance)
                {
                    minDistance = distance;
                    nearestStationIndex = i;
                }
            }

            return nearestStationIndex;
        }
    }
}
