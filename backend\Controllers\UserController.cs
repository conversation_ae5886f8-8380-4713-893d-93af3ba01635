using Microsoft.AspNetCore.Mvc;
using WeChatBus.Models;
using WeChatBus.Services;

namespace WeChatBus.Controllers
{
    [ApiController]
    [Route("api/user")]
    public class UserController : ControllerBase
    {
        private readonly IUserInfoService _userInfoService;
        private readonly ILogger<UserController> _logger;

        public UserController(
            IUserInfoService userInfoService,
            ILogger<UserController> logger)
        {
            _userInfoService = userInfoService;
            _logger = logger;
        }

        /// <summary>
        /// 微信登录获取OpenId
        /// </summary>
        /// <param name="request">登录请求</param>
        /// <returns>OpenId</returns>
        [HttpPost("login")]
        public async Task<ActionResult<ApiResponse<object>>> Login([FromBody] WeChatLoginRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.Code))
                {
                    return BadRequest(new ApiResponse<object>
                    {
                        Success = false,
                        Message = "登录凭证不能为空",
                        Code = 400
                    });
                }

                var openId = await _userInfoService.GetOpenIdByCodeAsync(request.Code);
                
                return Ok(new ApiResponse<object>
                {
                    Success = true,
                    Message = "获取OpenId成功",
                    Data = new { openid = openId }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "微信登录失败");
                return StatusCode(500, new ApiResponse<object>
                {
                    Success = false,
                    Message = "登录失败，请稍后重试",
                    Code = 500
                });
            }
        }

        /// <summary>
        /// 用户授权并保存信息
        /// </summary>
        /// <param name="request">授权请求</param>
        /// <returns>用户信息</returns>
        [HttpPost("auth")]
        public async Task<ActionResult<ApiResponse<UserInfoResponse>>> Auth([FromBody] UserAuthRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.OpenId) || 
                    string.IsNullOrEmpty(request.PhoneNumber) || 
                    string.IsNullOrEmpty(request.NickName))
                {
                    return BadRequest(new ApiResponse<UserInfoResponse>
                    {
                        Success = false,
                        Message = "必填信息不能为空",
                        Code = 400
                    });
                }

                var user = await _userInfoService.SaveUserAuthAsync(request);
                
                var userInfo = new UserInfoResponse
                {
                    OpenId = user.OpenId,
                    PhoneNumber = user.PhoneNumber,
                    NickName = user.NickName ?? "",
                    AvatarUrl = user.AvatarUrl ?? "",
                    CreatedAt = user.CreatedAt,
                    UpdatedAt = user.UpdatedAt
                };

                return Ok(new ApiResponse<UserInfoResponse>
                {
                    Success = true,
                    Message = "用户授权成功",
                    Data = userInfo
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "用户授权失败");
                return StatusCode(500, new ApiResponse<UserInfoResponse>
                {
                    Success = false,
                    Message = "授权失败，请稍后重试",
                    Code = 500
                });
            }
        }

        /// <summary>
        /// 获取用户信息
        /// </summary>
        /// <param name="openid">用户OpenId</param>
        /// <returns>用户信息</returns>
        [HttpGet("info")]
        public async Task<ActionResult<ApiResponse<UserInfoResponse>>> GetUserInfo([FromQuery] string openid)
        {
            try
            {
                if (string.IsNullOrEmpty(openid))
                {
                    return BadRequest(new ApiResponse<UserInfoResponse>
                    {
                        Success = false,
                        Message = "OpenId不能为空",
                        Code = 400
                    });
                }

                var user = await _userInfoService.GetUserByOpenIdAsync(openid);
                
                if (user == null)
                {
                    return Ok(new ApiResponse<UserInfoResponse>
                    {
                        Success = false,
                        Message = "用户不存在",
                        Code = 404,
                        Data = null
                    });
                }

                var userInfo = new UserInfoResponse
                {
                    OpenId = user.OpenId,
                    PhoneNumber = user.PhoneNumber,
                    NickName = user.NickName ?? "",
                    AvatarUrl = user.AvatarUrl ?? "",
                    CreatedAt = user.CreatedAt,
                    UpdatedAt = user.UpdatedAt
                };

                return Ok(new ApiResponse<UserInfoResponse>
                {
                    Success = true,
                    Message = "获取用户信息成功",
                    Data = userInfo
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取用户信息失败: {openid}");
                return StatusCode(500, new ApiResponse<UserInfoResponse>
                {
                    Success = false,
                    Message = "获取用户信息失败，请稍后重试",
                    Code = 500
                });
            }
        }
    }
}
