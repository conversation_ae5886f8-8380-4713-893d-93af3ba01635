-- 微信小程序班车查看系统 MySQL 数据库初始化脚本
-- 创建数据库
CREATE DATABASE IF NOT EXISTS `WeChatBusDb` 
DEFAULT CHARACTER SET utf8mb4 
DEFAULT COLLATE utf8mb4_unicode_ci;

USE `WeChatBusDb`;

-- 创建用户表
CREATE TABLE IF NOT EXISTS `Users` (
    `Id` INT AUTO_INCREMENT PRIMARY KEY,
    `PhoneNumber` VARCHAR(20) NOT NULL UNIQUE,
    `NickName` VARCHAR(100) NULL,
    `AvatarUrl` VARCHAR(500) NULL,
    `OpenId` VARCHAR(100) NULL,
    `UnionId` VARCHAR(100) NULL,
    `UserType` VARCHAR(20) NOT NULL DEFAULT 'visitor',
    `EmployeeId` VARCHAR(50) NULL,
    `Department` VARCHAR(100) NULL,
    `CreatedAt` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `LastLoginAt` DATETIME(6) NULL,
    `IsActive` BOOLEAN NOT NULL DEFAULT TRUE,
    INDEX `IX_Users_PhoneNumber` (`PhoneNumber`),
    INDEX `IX_Users_OpenId` (`OpenId`),
    INDEX `IX_Users_EmployeeId` (`EmployeeId`),
    INDEX `IX_Users_UserType` (`UserType`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建班车路线表
CREATE TABLE IF NOT EXISTS `BusRoutes` (
    `Id` INT AUTO_INCREMENT PRIMARY KEY,
    `Name` VARCHAR(50) NOT NULL,
    `CarId` VARCHAR(20) NOT NULL UNIQUE,
    `CarNumber` VARCHAR(20) NOT NULL,
    `Color` VARCHAR(20) NULL,
    `IsActive` BOOLEAN NOT NULL DEFAULT TRUE,
    `CreatedAt` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    INDEX `IX_BusRoutes_CarId` (`CarId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建班车站点表
CREATE TABLE IF NOT EXISTS `BusStations` (
    `Id` INT AUTO_INCREMENT PRIMARY KEY,
    `RouteId` INT NOT NULL,
    `Name` VARCHAR(100) NOT NULL,
    `Order` INT NOT NULL,
    `Latitude` DOUBLE NOT NULL,
    `Longitude` DOUBLE NOT NULL,
    FOREIGN KEY (`RouteId`) REFERENCES `BusRoutes`(`Id`) ON DELETE CASCADE,
    UNIQUE INDEX `IX_BusStations_RouteId_Order` (`RouteId`, `Order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建已过站点表
CREATE TABLE IF NOT EXISTS `PassedStations` (
    `Id` INT AUTO_INCREMENT PRIMARY KEY,
    `RouteId` INT NOT NULL,
    `StationId` INT NOT NULL,
    `CarId` VARCHAR(20) NOT NULL,
    `PassedTime` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `CreatedAt` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    FOREIGN KEY (`RouteId`) REFERENCES `BusRoutes`(`Id`) ON DELETE CASCADE,
    FOREIGN KEY (`StationId`) REFERENCES `BusStations`(`Id`) ON DELETE CASCADE,
    INDEX `IX_PassedStations_RouteId_CarId` (`RouteId`, `CarId`),
    INDEX `IX_PassedStations_StationId` (`StationId`),
    INDEX `IX_PassedStations_PassedTime` (`PassedTime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入初始班车路线数据
INSERT INTO `BusRoutes` (`Id`, `Name`, `CarId`, `CarNumber`, `Color`, `IsActive`, `CreatedAt`) VALUES
(1, '1号线', '1181', '闽DY1576', '#FF6B6B', FALSE, NOW(6)),
(2, '2号线', '1162', '闽DZ5829', '#4ECDC4', TRUE, NOW(6)),
(3, '3号线', '1171', '闽DX1686', '#45B7D1', FALSE, NOW(6)),
(4, '4号线', '5699', '闽DX3180', '#96CEB4', FALSE, NOW(6)),
(5, '5号线', '1168', '闽DZ9581', '#FFEAA7', FALSE, NOW(6))
ON DUPLICATE KEY UPDATE 
    `Name` = VALUES(`Name`),
    `CarNumber` = VALUES(`CarNumber`),
    `Color` = VALUES(`Color`),
    `IsActive` = VALUES(`IsActive`);

-- 插入2号线站点数据
INSERT INTO `BusStations` (`Id`, `RouteId`, `Name`, `Order`, `Latitude`, `Longitude`) VALUES
(1, 2, '嘉庚体育馆', 1, 24.5804, 118.0965),
(2, 2, '集美厂区', 2, 24.5756, 118.1123),
(3, 2, '霞梧路口', 3, 24.5698, 118.1287),
(4, 2, '叶厝', 4, 24.5634, 118.1456),
(5, 2, '禹州大学城', 5, 24.5589, 118.1623),
(6, 2, '洪塘头', 6, 24.5523, 118.1789),
(7, 2, '酱文化园', 7, 24.5467, 118.1954),
(8, 2, '翔安厂区', 8, 24.5412, 118.2123)
ON DUPLICATE KEY UPDATE 
    `Name` = VALUES(`Name`),
    `Order` = VALUES(`Order`),
    `Latitude` = VALUES(`Latitude`),
    `Longitude` = VALUES(`Longitude`);

-- 创建索引以优化查询性能
CREATE INDEX IF NOT EXISTS `IX_BusStations_RouteId` ON `BusStations` (`RouteId`);
CREATE INDEX IF NOT EXISTS `IX_BusStations_Order` ON `BusStations` (`Order`);
CREATE INDEX IF NOT EXISTS `IX_Users_CreatedAt` ON `Users` (`CreatedAt`);
CREATE INDEX IF NOT EXISTS `IX_BusRoutes_IsActive` ON `BusRoutes` (`IsActive`);
CREATE INDEX IF NOT EXISTS `IX_PassedStations_CreatedAt` ON `PassedStations` (`CreatedAt`);

-- 显示创建结果
SELECT 'Database initialization completed successfully!' as Status;
SELECT COUNT(*) as RouteCount FROM `BusRoutes`;
SELECT COUNT(*) as StationCount FROM `BusStations`;
SELECT COUNT(*) as PassedStationCount FROM `PassedStations`;

-- 查看创建的表结构
SHOW TABLES;
DESCRIBE `Users`;
DESCRIBE `BusRoutes`;
DESCRIBE `BusStations`;
DESCRIBE `PassedStations`;
