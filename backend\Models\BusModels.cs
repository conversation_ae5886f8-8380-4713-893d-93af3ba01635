using System.ComponentModel.DataAnnotations;

namespace WeChatBus.Models
{
    public class BusRoute
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [StringLength(20)]
        public string CarId { get; set; } = string.Empty;

        [Required]
        [StringLength(20)]
        public string CarNumber { get; set; } = string.Empty;

        [StringLength(20)]
        public string Color { get; set; } = string.Empty;

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public List<BusStation> Stations { get; set; } = new List<BusStation>();
    }

    public class BusStation
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        public int RouteId { get; set; }

        public int Order { get; set; }

        public double Latitude { get; set; }

        public double Longitude { get; set; }

        public BusRoute Route { get; set; } = null!;
    }

    public class BusLocationData
    {
        public string CarId { get; set; } = string.Empty;
        public double Lat { get; set; }
        public double Lng { get; set; }
        public string Addr { get; set; } = string.Empty;
        public int Speed { get; set; }
        public string Time { get; set; } = string.Empty;
        public int State { get; set; }

        // 新增位置分析字段
        public int CurrentStationIndex { get; set; } = -1;
        public string NearestStationName { get; set; } = string.Empty;
        public double DistanceToStation { get; set; } = 0;
        public bool IsAtStation { get; set; } = false;
        public string LocationStatus { get; set; } = string.Empty;
    }

    public class GpsApiResponse
    {
        public bool Encry { get; set; }
        public List<GpsVehicleData>? Result { get; set; }
        public int Status { get; set; }
    }

    public class GpsVehicleData
    {
        public string CarId { get; set; } = string.Empty;
        public string Addr { get; set; } = string.Empty;
        public double Lat { get; set; }
        public double Lng { get; set; }
        public int Speed { get; set; }
        public int State { get; set; }
        public string Time { get; set; } = string.Empty;
    }

    public class BusLocationResponse
    {
        public bool Success { get; set; }
        public BusLocationData? Data { get; set; }
        public string? Message { get; set; }
    }

    public class RouteInfoResponse
    {
        public bool Success { get; set; }
        public RouteInfo? Data { get; set; }
        public string? Message { get; set; }
    }

    public class RouteInfo
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string CarNumber { get; set; } = string.Empty;
        public string Color { get; set; } = string.Empty;
        public List<StationInfo> Stations { get; set; } = new List<StationInfo>();
    }

    public class StationInfo
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public int Order { get; set; }
        public double Latitude { get; set; }
        public double Longitude { get; set; }
    }

    /// <summary>
    /// 班车线路DTO - 用于API返回
    /// </summary>
    public class BusRouteDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string CarId { get; set; } = string.Empty;
        public string CarNumber { get; set; } = string.Empty;
        public string Color { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public string Status { get; set; } = string.Empty;
        public string StatusText { get; set; } = string.Empty;
        public List<int> PassedStationIds { get; set; } = new List<int>();
        public List<BusStationDto> Stations { get; set; } = new List<BusStationDto>();
    }

    /// <summary>
    /// 班车站点DTO - 用于API返回
    /// </summary>
    public class BusStationDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public int Order { get; set; }
        public double Latitude { get; set; }
        public double Longitude { get; set; }
        public bool IsPassed { get; set; }
    }
}
