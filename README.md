# 微信小程序班车查看系统

一个基于微信小程序的班车实时位置查看系统，包含前端微信小程序和后端.NET Core API服务。

## 功能特性

- 🚌 **实时位置追踪**: 通过第三方GPS接口获取班车实时位置
- 📱 **微信小程序**: 原生微信小程序体验，支持手机号快速登录
- 🗺️ **线路展示**: 可视化展示班车线路和站点信息
- 🔄 **自动刷新**: 每30秒自动更新班车位置信息
- 🎨 **美观界面**: 现代化UI设计，支持滑动查看站点

## 技术栈

### 前端
- 微信小程序原生开发
- JavaScript ES6+
- WXSS样式

### 后端
- .NET 8.0 Web API
- Entity Framework Core
- JWT身份验证
- SQL Server数据库

### 第三方服务
- GPS车辆定位系统
- 微信小程序API

## 项目结构

```
wechatcar/
├── miniprogram/                 # 微信小程序前端
│   ├── pages/                   # 页面
│   │   ├── index/              # 首页
│   │   ├── login/              # 登录页
│   │   └── route/              # 线路详情页
│   ├── utils/                  # 工具类
│   ├── app.js                  # 小程序入口
│   ├── app.json               # 小程序配置
│   └── app.wxss               # 全局样式
├── backend/                    # .NET Core 后端
│   ├── Controllers/            # 控制器
│   ├── Models/                 # 数据模型
│   ├── Services/               # 业务服务
│   ├── Data/                   # 数据访问
│   └── Program.cs              # 程序入口
├── 开发指导文档.md              # 详细开发文档
├── 部署运行指南.md              # 部署指南
└── README.md                   # 本文件
```

## 快速开始

### 环境要求

- .NET 8.0 SDK
- SQL Server 2019+ 或 LocalDB
- 微信开发者工具
- Visual Studio 2022 或 VS Code

### 1. 克隆项目

```bash
git clone <repository-url>
cd wechatcar
```

### 2. 后端设置

```bash
cd backend

# 安装依赖
dotnet restore

# 配置数据库连接字符串
# 编辑 appsettings.json 中的 ConnectionStrings

# 创建数据库
dotnet ef database update

# 运行后端服务
dotnet run
```

后端服务将在 `https://localhost:7000` 启动。

### 3. 前端设置

1. 打开微信开发者工具
2. 导入项目，选择 `miniprogram` 目录
3. 配置AppID（测试可使用测试号）
4. 修改 `miniprogram/app.js` 中的 `baseUrl` 为后端API地址
5. 点击编译运行

### 4. 配置说明

#### 后端配置 (appsettings.json)

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=WeChatBusDb;Trusted_Connection=true"
  },
  "WeChat": {
    "AppId": "your_wechat_appid",
    "AppSecret": "your_wechat_appsecret"
  }
}
```

#### 微信小程序配置

在微信公众平台配置服务器域名白名单：
- request合法域名: `https://your-api-domain.com`

## 线路信息

目前支持的班车线路：

| 线路 | 车牌号 | 状态 | 站点 |
|------|--------|------|------|
| 1号线 | 闽DY1576 | 即将接入 | - |
| 2号线 | 闽DZ5829 | ✅ 可用 | 嘉庚体育馆 → 集美厂区 → 霞梧路口 → 叶厝 → 禹州大学城 → 洪塘头 → 酱文化园 → 翔安厂区 |
| 3号线 | 闽DX1686 | 即将接入 | - |
| 4号线 | 闽DX3180 | 即将接入 | - |
| 5号线 | 闽DZ9581 | 即将接入 | - |

## API接口

### 认证接口

- `POST /api/auth/login` - 用户登录
- `POST /api/auth/decrypt-phone` - 解密微信手机号

### 班车接口

- `GET /api/bus/routes` - 获取所有线路
- `GET /api/bus/route/{routeId}` - 获取指定线路信息
- `GET /api/bus/location/{carId}` - 获取班车实时位置

## 开发指南

详细的开发指南请参考：
- [开发指导文档.md](./开发指导文档.md) - 完整的开发指导
- [部署运行指南.md](./部署运行指南.md) - 部署和运行说明

## 主要功能截图

### 登录页面
- 微信授权登录
- 获取手机号码

### 首页
- 5条线路展示
- 线路状态显示
- 美观的卡片式布局

### 线路详情页
- 实时班车位置
- 站点信息展示
- 左右滑动查看站点
- 自动刷新位置

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 提交 Issue
- 发送邮件到: [<EMAIL>]

## 更新日志

### v1.0.0 (2025-01-23)
- ✨ 初始版本发布
- 🚌 支持2号线实时位置查看
- 📱 微信小程序登录功能
- 🗺️ 站点信息展示
- 🔄 自动刷新功能
