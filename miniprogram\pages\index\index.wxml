<!--pages/index/index.wxml-->
<view class="container">
  <!-- 简洁头部 -->
  <view class="header">
    <view class="title">厦华云班车</view>
    <view class="user-info" wx:if="{{!needAuth && userInfo}}">
      <text class="user-name">{{userInfo.nickName}}</text>
      <text class="phone">{{userInfo.phoneNumber}}</text>
      <text class="reauth-btn" bindtap="reAuth">重新授权</text>
    </view>
    <view class="auth-info" wx:else>
      <text class="auth-text">请先授权使用</text>
      <text class="auth-btn" bindtap="showAuthModal">立即授权</text>
    </view>
  </view>

  <!-- 时间状态 -->
  <view class="time-status">
    <text class="time-text">服务时间：6:00-9:00</text>
    <view class="status-dot {{isInTimeRange ? 'active' : 'inactive'}}"></view>
  </view>

  <!-- 内容区域 -->
  <view class="content">
    <!-- 需要授权 -->
    <view class="auth-notice" wx:if="{{needAuth}}">
      <view class="notice-icon">🔐</view>
      <text class="notice-title">需要授权使用</text>
      <text class="notice-desc">请授权获取您的基本信息和手机号</text>
      <button class="auth-button" bindtap="showAuthModal">立即授权</button>
    </view>

    <!-- 加载状态 -->
    <view class="loading" wx:elif="{{isLoading}}">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 时间范围外 -->
    <view class="time-notice" wx:elif="{{!isInTimeRange}}">
      <view class="notice-icon">⏰</view>
      <text class="notice-text">不在服务时间范围内</text>
    </view>

    <!-- 班车列表 -->
    <view class="bus-list" wx:else>
      <view
        class="bus-item"
        wx:for="{{routes}}"
        wx:key="id"
        bindtap="onRouteClick"
        data-route="{{item}}"
      >
        <view class="bus-info">
          <view class="bus-name">{{item.name}}</view>
          <view class="bus-car">{{item.carNumber}}</view>
        </view>
        <view class="bus-status">
          <view class="status-dot active"></view>
          <text class="status-text">{{item.statusText}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 授权弹窗 -->
  <view class="auth-modal" wx:if="{{showAuthModal}}">
    <view class="modal-mask" bindtap="closeAuthModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">授权使用</text>
        <text class="close-btn" bindtap="closeAuthModal">×</text>
      </view>
      <view class="modal-body">
        <view class="auth-icon">👤</view>
        <text class="auth-desc">为了给您提供更好的服务，需要获取您的基本信息和手机号</text>
      </view>
      <view class="modal-footer">
        <button class="cancel-btn" bindtap="closeAuthModal">取消</button>
        <button
          class="confirm-btn"
          open-type="getPhoneNumber"
          bindgetphonenumber="getPhoneNumber"
        >
          授权
        </button>
      </view>
    </view>
  </view>
</view>
