using WeChatBus.Models;

namespace WeChatBus.Services
{
    public interface IGpsService
    {
        Task<string?> LoginAsync();
        Task<string?> GetAuthorizationTokenAsync(string sessionId);
        Task<List<GpsVehicleData>> GetVehicleLocationsAsync(string[] carIds);
    }

    public class GpsService : IGpsService
    {
        private readonly HttpClient _httpClient;
        private readonly IConfiguration _configuration;
        private readonly ILogger<GpsService> _logger;

        private const string BASE_URL = "http://121.41.14.18:9999/gps-web/h5";
        private const string LOGIN_USER = "prima";
        private const string LOGIN_PASSWORD = "d002cce301a6f1d63391d94e8d32b9ff";

        public GpsService(HttpClient httpClient, IConfiguration configuration, ILogger<GpsService> logger)
        {
            _httpClient = httpClient;
            _configuration = configuration;
            _logger = logger;
        }

        public async Task<string?> LoginAsync()
        {
            try
            {
                var timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
                var loginUrl = $"{BASE_URL}/login?_t={timestamp}";

                var loginData = new
                {
                    loginType = "user",
                    userId = LOGIN_USER,
                    password = LOGIN_PASSWORD,
                    rsaId = (string?)null,
                    plateColor = "1",
                    smsCode = (string?)null,
                    code = new[] { new { x = 1361, y = 315, t = 0 } },
                    imgId = "",
                    imgCode = (string?)null,
                    loginLang = "zh_CN",
                    loginWay = "ie",
                    h5login = true
                };

                var response = await _httpClient.PostAsJsonAsync(loginUrl, loginData);
                var responseContent = await response.Content.ReadAsStringAsync();

                _logger.LogInformation($"GPS Login Response: {responseContent}");

                if (response.IsSuccessStatusCode)
                {
                    var loginResponse = System.Text.Json.JsonSerializer.Deserialize<GpsLoginResponse>(responseContent);
                    if (loginResponse?.Status == 1 && !string.IsNullOrEmpty(loginResponse.Result))
                    {
                        // 提取sessionId (格式: "ok#sessionId")
                        var parts = loginResponse.Result.Split('#');
                        if (parts.Length == 2 && parts[0] == "ok")
                        {
                            return parts[1];
                        }
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GPS登录失败");
                return null;
            }
        }

        public async Task<string?> GetAuthorizationTokenAsync(string sessionId)
        {
            try
            {
                var timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
                var authUrl = $"{BASE_URL}/alogin?sessionId={sessionId}&scheme=http&_t={timestamp}";

                var response = await _httpClient.GetAsync(authUrl);
                
                if (response.IsSuccessStatusCode)
                {
                    // 从响应头中获取Authorization token
                    if (response.Headers.TryGetValues("Authorization", out var authValues))
                    {
                        return authValues.FirstOrDefault();
                    }
                    
                    // 如果响应头中没有，尝试从响应内容中解析
                    var responseContent = await response.Content.ReadAsStringAsync();
                    _logger.LogInformation($"GPS Auth Response: {responseContent}");
                    
                    // 这里需要根据实际响应格式来解析token
                    // 暂时返回sessionId作为token
                    return sessionId;
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取GPS授权token失败");
                return null;
            }
        }

        public async Task<List<GpsVehicleData>> GetVehicleLocationsAsync(string[] carIds)
        {
            try
            {
                // 先登录获取sessionId
                var sessionId = await LoginAsync();
                if (string.IsNullOrEmpty(sessionId))
                {
                    _logger.LogError("GPS登录失败，无法获取车辆位置");
                    return new List<GpsVehicleData>();
                }

                // 获取授权token
                var token = await GetAuthorizationTokenAsync(sessionId);
                if (string.IsNullOrEmpty(token))
                {
                    _logger.LogError("获取GPS授权token失败");
                    return new List<GpsVehicleData>();
                }

                // 请求车辆位置
                var locationUrl = $"{BASE_URL}/mnt/rtl";
                
                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("Authorization", token);
                _httpClient.DefaultRequestHeaders.Add("addCarIds", "");

                var response = await _httpClient.PostAsJsonAsync(locationUrl, carIds);
                var responseContent = await response.Content.ReadAsStringAsync();

                _logger.LogInformation($"GPS Location Response: {responseContent}");

                if (response.IsSuccessStatusCode)
                {
                    var locationResponse = System.Text.Json.JsonSerializer.Deserialize<GpsApiResponse>(responseContent);
                    if (locationResponse?.Status == 1 && locationResponse.Result != null)
                    {
                        return locationResponse.Result;
                    }
                }

                return new List<GpsVehicleData>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取车辆位置失败");
                return new List<GpsVehicleData>();
            }
        }
    }

    public class GpsLoginResponse
    {
        public bool Encry { get; set; }
        public string Result { get; set; } = string.Empty;
        public int Status { get; set; }
    }
}
