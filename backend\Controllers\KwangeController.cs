using Microsoft.AspNetCore.Mvc;
using WeChatBus.Models;
using WeChatBus.Services;

namespace WeChatBus.Controllers
{
    [ApiController]
    [Route("api/kwange")]
    public class KwangeController : ControllerBase
    {
        private readonly IStationService _stationService;
        private readonly IUserInfoService _userInfoService;
        private readonly ILogger<KwangeController> _logger;

        public KwangeController(
            IStationService stationService,
            IUserInfoService userInfoService,
            ILogger<KwangeController> logger)
        {
            _stationService = stationService;
            _userInfoService = userInfoService;
            _logger = logger;
        }

        /// <summary>
        /// 获取当前时间段内可查询的班车列表
        /// </summary>
        /// <returns>可查询的班车线路列表</returns>
        [HttpGet("buses/available")]
        public async Task<ActionResult<ApiResponse<List<BusRouteDto>>>> GetAvailableBuses()
        {
            try
            {
                // 检查当前时间是否在可查询范围内（6:00-9:00）
                var currentTime = DateTime.Now;
                var currentHour = currentTime.Hour;

                if (currentHour < 6 || currentHour >= 9)
                {
                    return Ok(new ApiResponse<List<BusRouteDto>>
                    {
                        Success = false,
                        Message = "当前时间不在可查询范围内，可查询时间为早上6:00-9:00",
                        Code = 403,
                        Data = new List<BusRouteDto>()
                    });
                }

                // 获取可查询的班车列表
                var availableBuses = await _stationService.GetAvailableBusesAsync();

                return Ok(new ApiResponse<List<BusRouteDto>>
                {
                    Success = true,
                    Message = "获取可查询班车列表成功",
                    Data = availableBuses
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取可查询班车列表时发生错误");
                return StatusCode(500, new ApiResponse<List<BusRouteDto>>
                {
                    Success = false,
                    Message = "服务器内部错误",
                    Code = 500,
                    Data = new List<BusRouteDto>()
                });
            }
        }

        /// <summary>
        /// 更新指定线路车辆已经过的站点
        /// </summary>
        /// <param name="routeId">线路ID</param>
        /// <param name="request">已过站点请求</param>
        /// <returns></returns>
        [HttpPost("route/{routeId}/passed-stations")]
        public async Task<ActionResult<ApiResponse<bool>>> UpdatePassedStations(
            int routeId, 
            [FromBody] UpdatePassedStationsRequest request)
        {
            try
            {
                if (request == null || !request.StationIds.Any())
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        Success = false,
                        Message = "站点ID列表不能为空",
                        Code = 400
                    });
                }

                var result = await _stationService.UpdatePassedStationsAsync(routeId, request);
                
                if (result.Success)
                {
                    _logger.LogInformation($"更新线路 {routeId} 已过站点成功");
                    return Ok(result);
                }
                else
                {
                    _logger.LogWarning($"更新线路 {routeId} 已过站点失败: {result.Message}");
                    return result.Code switch
                    {
                        404 => NotFound(result),
                        400 => BadRequest(result),
                        _ => StatusCode(500, result)
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"更新线路 {routeId} 已过站点接口异常");
                return StatusCode(500, new ApiResponse<bool>
                {
                    Success = false,
                    Message = "服务器内部错误",
                    Code = 500
                });
            }
        }

        /// <summary>
        /// 获取指定线路车辆已经过的所有站点列表
        /// </summary>
        /// <param name="routeId">线路ID</param>
        /// <returns></returns>
        [HttpGet("route/{routeId}/passed-stations")]
        public async Task<ActionResult<PassedStationResponse>> GetPassedStations(int routeId)
        {
            try
            {
                var result = await _stationService.GetPassedStationsAsync(routeId);
                
                if (result.Success)
                {
                    _logger.LogInformation($"获取线路 {routeId} 已过站点成功");
                    return Ok(result);
                }
                else
                {
                    _logger.LogWarning($"获取线路 {routeId} 已过站点失败: {result.Message}");
                    return NotFound(result);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取线路 {routeId} 已过站点接口异常");
                return StatusCode(500, new PassedStationResponse
                {
                    Success = false,
                    Message = "服务器内部错误"
                });
            }
        }

        /// <summary>
        /// 获取所有线路信息（包含站点和已过站点状态）- 用于小程序首页一次性加载
        /// </summary>
        /// <returns></returns>
        [HttpGet("routes")]
        public async Task<ActionResult<ApiResponse<List<SimpleRouteInfo>>>> GetAllRoutesWithStations()
        {
            try
            {
                var result = await _stationService.GetAllRoutesWithStationsAsync();
                
                if (result.Success)
                {
                    _logger.LogInformation("获取所有线路信息成功");
                    return Ok(result);
                }
                else
                {
                    _logger.LogWarning($"获取所有线路信息失败: {result.Message}");
                    return StatusCode(500, result);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有线路信息接口异常");
                return StatusCode(500, new ApiResponse<List<SimpleRouteInfo>>
                {
                    Success = false,
                    Message = "服务器内部错误",
                    Code = 500
                });
            }
        }

        /// <summary>
        /// 存储微信用户基本信息
        /// </summary>
        /// <param name="request">用户信息请求</param>
        /// <returns></returns>
        [HttpPost("user/info")]
        public async Task<ActionResult<UserInfoResponse>> SaveUserInfo([FromBody] UserInfoRequest request)
        {
            try
            {
                if (request == null || string.IsNullOrEmpty(request.PhoneNumber))
                {
                    return BadRequest(new UserInfoResponse
                    {
                        Success = false,
                        Message = "手机号不能为空"
                    });
                }

                var result = await _userInfoService.SaveUserInfoAsync(request);
                
                if (result.Success)
                {
                    _logger.LogInformation($"保存用户信息成功: {request.PhoneNumber}");
                    return Ok(result);
                }
                else
                {
                    _logger.LogWarning($"保存用户信息失败: {request.PhoneNumber}, 原因: {result.Message}");
                    return BadRequest(result);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"保存用户信息接口异常: {request?.PhoneNumber}");
                return StatusCode(500, new UserInfoResponse
                {
                    Success = false,
                    Message = "服务器内部错误"
                });
            }
        }

        /// <summary>
        /// 获取用户信息
        /// </summary>
        /// <param name="phoneNumber">手机号</param>
        /// <returns></returns>
        [HttpGet("user/info/{phoneNumber}")]
        public async Task<ActionResult<ApiResponse<ExtendedUserInfo>>> GetUserInfo(string phoneNumber)
        {
            try
            {
                if (string.IsNullOrEmpty(phoneNumber))
                {
                    return BadRequest(new ApiResponse<ExtendedUserInfo>
                    {
                        Success = false,
                        Message = "手机号不能为空",
                        Code = 400
                    });
                }

                var result = await _userInfoService.GetUserInfoAsync(phoneNumber);
                
                if (result.Success)
                {
                    return Ok(result);
                }
                else
                {
                    return result.Code switch
                    {
                        404 => NotFound(result),
                        _ => StatusCode(500, result)
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取用户信息接口异常: {phoneNumber}");
                return StatusCode(500, new ApiResponse<ExtendedUserInfo>
                {
                    Success = false,
                    Message = "服务器内部错误",
                    Code = 500
                });
            }
        }

        /// <summary>
        /// 根据用户类型获取用户列表（为后续员工权限功能预留）
        /// </summary>
        /// <param name="userType">用户类型：employee-员工, visitor-访客</param>
        /// <returns></returns>
        [HttpGet("users/{userType}")]
        public async Task<ActionResult<ApiResponse<List<ExtendedUserInfo>>>> GetUsersByType(string userType)
        {
            try
            {
                if (string.IsNullOrEmpty(userType))
                {
                    return BadRequest(new ApiResponse<List<ExtendedUserInfo>>
                    {
                        Success = false,
                        Message = "用户类型不能为空",
                        Code = 400
                    });
                }

                var result = await _userInfoService.GetUsersByTypeAsync(userType);
                
                if (result.Success)
                {
                    return Ok(result);
                }
                else
                {
                    return StatusCode(500, result);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取用户列表接口异常: {userType}");
                return StatusCode(500, new ApiResponse<List<ExtendedUserInfo>>
                {
                    Success = false,
                    Message = "服务器内部错误",
                    Code = 500
                });
            }
        }

        /// <summary>
        /// 健康检查接口
        /// </summary>
        /// <returns></returns>
        [HttpGet("health")]
        public ActionResult<ApiResponse<object>> Health()
        {
            return Ok(new ApiResponse<object>
            {
                Success = true,
                Data = new
                {
                    Service = "Kwange API",
                    Status = "Running",
                    Version = "1.0.0"
                },
                Message = "服务运行正常"
            });
        }
    }
}
