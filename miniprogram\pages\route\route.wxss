/* pages/route/route.wxss */
.container {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 30rpx;
}

.bus-info-card {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.bus-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.bus-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.icon-text {
  font-size: 36rpx;
  color: white;
}

.bus-details {
  flex: 1;
}

.bus-name {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8rpx;
}

.bus-number {
  font-size: 26rpx;
  color: #666666;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.auto-refresh-btn {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  background: #f0f0f0;
  border: 2rpx solid #e0e0e0;
  transition: all 0.3s ease;
}

.auto-refresh-btn.active {
  background: #4ECDC4;
  border-color: #4ECDC4;
}

.refresh-status {
  font-size: 22rpx;
  color: #666666;
}

.auto-refresh-btn.active .refresh-status {
  color: #ffffff;
}

.refresh-btn {
  padding: 10rpx;
}

.refresh-icon {
  font-size: 32rpx;
  color: #666;
}

.bus-status {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 20rpx;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.status-item:last-child {
  margin-bottom: 0;
}

.status-label {
  font-size: 26rpx;
  color: #666666;
}

.status-value {
  font-size: 26rpx;
  color: #333333;
  font-weight: 500;
}

.distance-info {
  color: #4ECDC4;
  font-size: 22rpx;
}

.status-at-station {
  color: #28a745 !important;
  font-weight: bold;
}

.status-moving {
  color: #ffc107 !important;
}

.loading {
  text-align: center;
  padding: 40rpx 0;
}

.loading-text {
  font-size: 26rpx;
  color: #999999;
}

.route-section {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.section-header {
  margin-bottom: 30rpx;
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8rpx;
}

.section-desc {
  font-size: 24rpx;
  color: #999999;
}

.stations-container {
  width: 100%;
  white-space: nowrap;
}

.stations-wrapper {
  display: inline-flex;
  align-items: flex-start;
  position: relative;
  padding: 20rpx 0;
}

.connection-line {
  position: absolute;
  top: 50rpx;
  left: 40rpx;
  right: 40rpx;
  height: 4rpx;
  background: #e0e0e0;
  z-index: 1;
}

.station-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 60rpx;
  position: relative;
  z-index: 2;
}

.station-item:last-child {
  margin-right: 0;
}

.station-dot {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15rpx;
  position: relative;
}

.station-item.waiting .station-dot {
  background: #f0f0f0;
  border: 4rpx solid #e0e0e0;
}

.station-item.current .station-dot {
  background: #4ECDC4;
  border: 4rpx solid #ffffff;
  box-shadow: 0 0 0 4rpx #4ECDC4;
  animation: pulse 2s infinite;
}

.station-item.approaching .station-dot {
  background: #ffc107;
  border: 4rpx solid #ffffff;
  box-shadow: 0 0 0 4rpx #ffc107;
}

.station-item.passed .station-dot {
  background: #4ECDC4;
  border: 4rpx solid #ffffff;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 4rpx #4ECDC4;
  }
  50% {
    box-shadow: 0 0 0 8rpx rgba(78, 205, 196, 0.5);
  }
  100% {
    box-shadow: 0 0 0 4rpx #4ECDC4;
  }
}

.dot-inner {
  width: 20rpx;
  height: 20rpx;
  border-radius: 10rpx;
  background: #ffffff;
}

.station-item.waiting .dot-inner {
  background: #cccccc;
}

.bus-marker {
  position: absolute;
  top: -20rpx;
  left: 50%;
  transform: translateX(-50%);
}

.bus-icon-small {
  font-size: 24rpx;
  color: #4ECDC4;
}

.station-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 120rpx;
}

.station-name {
  font-size: 24rpx;
  color: #333333;
  font-weight: 500;
  text-align: center;
  margin-bottom: 5rpx;
  line-height: 1.2;
}

.station-status {
  font-size: 20rpx;
  color: #999999;
  text-align: center;
}

.station-item.current .station-status {
  color: #4ECDC4;
  font-weight: bold;
}

.tips-section {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.tip-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-icon {
  font-size: 28rpx;
  margin-right: 15rpx;
  display: inline-block;
  width: 32rpx;
  text-align: center;
}

.tip-text {
  font-size: 24rpx;
  color: #666666;
}
