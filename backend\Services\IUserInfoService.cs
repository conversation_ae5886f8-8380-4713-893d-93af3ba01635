using Microsoft.EntityFrameworkCore;
using WeChatBus.Data;
using WeChatBus.Models;

namespace WeChatBus.Services
{
    public interface IUserInfoService
    {
        Task<UserInfoResponse> SaveUserInfoAsync(UserInfoRequest request);
        Task<ApiResponse<ExtendedUserInfo>> GetUserInfoAsync(string phoneNumber);
        Task<ApiResponse<List<ExtendedUserInfo>>> GetUsersByTypeAsync(string userType);

        // 新增方法
        Task<User?> GetUserByOpenIdAsync(string openId);
        Task<User> SaveUserAuthAsync(UserAuthRequest request);
        Task<string> GetOpenIdByCodeAsync(string code);
    }

    public class UserInfoService : IUserInfoService
    {
        private readonly AppDbContext _context;
        private readonly ILogger<UserInfoService> _logger;

        public UserInfoService(AppDbContext context, ILogger<UserInfoService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<UserInfoResponse> SaveUserInfoAsync(UserInfoRequest request)
        {
            try
            {
                // 查找现有用户
                var existingUser = await _context.Users
                    .FirstOrDefaultAsync(u => u.PhoneNumber == request.PhoneNumber);

                User user;
                bool isNewUser = false;

                if (existingUser == null)
                {
                    // 创建新用户
                    user = new User
                    {
                        PhoneNumber = request.PhoneNumber,
                        NickName = request.NickName,
                        AvatarUrl = request.AvatarUrl,
                        OpenId = request.OpenId,
                        UnionId = request.UnionId,
                        UserType = request.UserType,
                        EmployeeId = request.EmployeeId,
                        Department = request.Department,
                        CreatedAt = DateTime.Now,
                        LastLoginAt = DateTime.Now,
                        IsActive = true
                    };

                    _context.Users.Add(user);
                    isNewUser = true;
                    _logger.LogInformation($"创建新用户: {request.PhoneNumber}");
                }
                else
                {
                    // 更新现有用户信息
                    existingUser.NickName = request.NickName ?? existingUser.NickName;
                    existingUser.AvatarUrl = request.AvatarUrl ?? existingUser.AvatarUrl;
                    existingUser.OpenId = request.OpenId ?? existingUser.OpenId;
                    existingUser.UnionId = request.UnionId ?? existingUser.UnionId;
                    existingUser.UserType = request.UserType;
                    existingUser.EmployeeId = request.EmployeeId ?? existingUser.EmployeeId;
                    existingUser.Department = request.Department ?? existingUser.Department;
                    existingUser.LastLoginAt = DateTime.Now;

                    user = existingUser;
                    _logger.LogInformation($"更新用户信息: {request.PhoneNumber}");
                }

                await _context.SaveChangesAsync();

                var userInfo = new UserInfo
                {
                    Id = user.Id,
                    PhoneNumber = user.PhoneNumber,
                    NickName = user.NickName,
                    AvatarUrl = user.AvatarUrl
                };

                return new UserInfoResponse
                {
                    Success = true,
                    Data = userInfo,
                    Message = isNewUser ? "用户创建成功" : "用户信息更新成功"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"保存用户信息失败: {request.PhoneNumber}");
                return new UserInfoResponse
                {
                    Success = false,
                    Message = "保存用户信息失败，请稍后重试"
                };
            }
        }

        public async Task<ApiResponse<ExtendedUserInfo>> GetUserInfoAsync(string phoneNumber)
        {
            try
            {
                var user = await _context.Users
                    .FirstOrDefaultAsync(u => u.PhoneNumber == phoneNumber);

                if (user == null)
                {
                    return new ApiResponse<ExtendedUserInfo>
                    {
                        Success = false,
                        Message = "用户不存在",
                        Code = 404
                    };
                }

                var userInfo = new ExtendedUserInfo
                {
                    Id = user.Id,
                    PhoneNumber = user.PhoneNumber,
                    NickName = user.NickName,
                    AvatarUrl = user.AvatarUrl,
                    OpenId = user.OpenId,
                    UnionId = user.UnionId,
                    UserType = user.UserType,
                    EmployeeId = user.EmployeeId,
                    Department = user.Department,
                    CreatedAt = user.CreatedAt,
                    LastLoginAt = user.LastLoginAt
                };

                return new ApiResponse<ExtendedUserInfo>
                {
                    Success = true,
                    Data = userInfo,
                    Message = "获取成功"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取用户信息失败: {phoneNumber}");
                return new ApiResponse<ExtendedUserInfo>
                {
                    Success = false,
                    Message = "获取用户信息失败，请稍后重试",
                    Code = 500
                };
            }
        }

        public async Task<ApiResponse<List<ExtendedUserInfo>>> GetUsersByTypeAsync(string userType)
        {
            try
            {
                var users = await _context.Users
                    .Where(u => u.UserType == userType && u.IsActive)
                    .OrderByDescending(u => u.LastLoginAt)
                    .ToListAsync();

                var userInfos = users.Select(user => new ExtendedUserInfo
                {
                    Id = user.Id,
                    PhoneNumber = user.PhoneNumber,
                    NickName = user.NickName,
                    AvatarUrl = user.AvatarUrl,
                    OpenId = user.OpenId,
                    UnionId = user.UnionId,
                    UserType = user.UserType,
                    EmployeeId = user.EmployeeId,
                    Department = user.Department,
                    CreatedAt = user.CreatedAt,
                    LastLoginAt = user.LastLoginAt
                }).ToList();

                return new ApiResponse<List<ExtendedUserInfo>>
                {
                    Success = true,
                    Data = userInfos,
                    Message = "获取成功"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取用户列表失败: {userType}");
                return new ApiResponse<List<ExtendedUserInfo>>
                {
                    Success = false,
                    Message = "获取用户列表失败，请稍后重试",
                    Code = 500
                };
            }
        }

        // 新增方法实现
        public async Task<User?> GetUserByOpenIdAsync(string openId)
        {
            try
            {
                return await _context.Users
                    .FirstOrDefaultAsync(u => u.OpenId == openId && u.IsActive);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"根据OpenId获取用户失败: {openId}");
                return null;
            }
        }

        public async Task<User> SaveUserAuthAsync(UserAuthRequest request)
        {
            try
            {
                // 查找现有用户
                var existingUser = await _context.Users
                    .FirstOrDefaultAsync(u => u.OpenId == request.OpenId);

                if (existingUser != null)
                {
                    // 更新现有用户信息
                    existingUser.PhoneNumber = request.PhoneNumber;
                    existingUser.NickName = request.NickName;
                    existingUser.AvatarUrl = request.AvatarUrl;
                    existingUser.UpdatedAt = DateTime.Now;
                    existingUser.LastLoginAt = DateTime.Now;

                    _context.Users.Update(existingUser);
                    await _context.SaveChangesAsync();

                    _logger.LogInformation($"更新用户信息成功: {request.OpenId}");
                    return existingUser;
                }
                else
                {
                    // 创建新用户
                    var newUser = new User
                    {
                        OpenId = request.OpenId,
                        PhoneNumber = request.PhoneNumber,
                        NickName = request.NickName,
                        AvatarUrl = request.AvatarUrl,
                        UserType = "visitor",
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now,
                        LastLoginAt = DateTime.Now,
                        IsActive = true
                    };

                    _context.Users.Add(newUser);
                    await _context.SaveChangesAsync();

                    _logger.LogInformation($"创建新用户成功: {request.OpenId}");
                    return newUser;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"保存用户授权信息失败: {request.OpenId}");
                throw;
            }
        }

        public async Task<string> GetOpenIdByCodeAsync(string code)
        {
            try
            {
                // 这里需要调用微信API获取openid
                // 为了简化，这里返回一个模拟的openid
                // 实际项目中需要调用微信的code2Session接口

                using var httpClient = new HttpClient();
                var appId = "your_app_id"; // 从配置中获取
                var appSecret = "your_app_secret"; // 从配置中获取

                var url = $"https://api.weixin.qq.com/sns/jscode2session?appid={appId}&secret={appSecret}&js_code={code}&grant_type=authorization_code";

                var response = await httpClient.GetStringAsync(url);
                // 解析响应获取openid
                // 这里简化处理，实际需要解析JSON

                // 临时返回模拟数据
                return $"mock_openid_{code}";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取OpenId失败: {code}");
                throw;
            }
        }
    }
}
