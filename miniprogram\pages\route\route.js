// pages/route/route.js
const app = getApp()

Page({
  data: {
    routeData: null,
    routeInfo: null,
    stations: [],
    passedStationIds: [],
    busLocation: null,
    refreshTimer: null,
    isLoading: false,
    lastUpdateTime: '',
    autoRefreshEnabled: true
  },

  onLoad(options) {
    try {
      // 从URL参数中解析完整的线路数据
      const routeData = JSON.parse(decodeURIComponent(options.routeData))

      this.setData({
        routeData: routeData,
        routeInfo: {
          id: routeData.id,
          name: routeData.name,
          carNumber: routeData.carNumber,
          color: routeData.color
        },
        stations: routeData.stations.map(station => ({
          ...station,
          status: station.isPassed ? 'passed' : 'waiting'
        })),
        passedStationIds: routeData.passedStationIds || []
      })

      // 设置页面标题
      wx.setNavigationBarTitle({
        title: routeData.name
      })

      // 开始获取实时位置
      this.getBusLocation()
      this.startAutoRefresh()

    } catch (error) {
      console.error('解析线路数据失败:', error)
      wx.showToast({
        title: '页面数据错误',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 2000)
    }
  },

  onShow() {
    // 页面显示时恢复自动刷新
    if (!this.data.refreshTimer && this.data.autoRefreshEnabled) {
      this.startAutoRefresh()
    }
  },

  onHide() {
    // 页面隐藏时暂停自动刷新以节省资源
    this.stopAutoRefresh()
  },

  onUnload() {
    // 清除定时器
    this.stopAutoRefresh()
  },

  // 获取已过站点信息
  async getPassedStations() {
    try {
      const response = await app.request({
        url: `/kwange/route/${this.data.routeData.id}/passed-stations`,
        method: 'GET'
      })

      if (response.success && response.data) {
        const passedStationIds = response.data.map(item => item.stationId)
        this.setData({ passedStationIds })
        this.updateStationStatus(passedStationIds)
      }
    } catch (error) {
      console.error('获取已过站点失败:', error)
    }
  },

  // 更新站点状态
  updateStationStatus(passedStationIds) {
    const updatedStations = this.data.stations.map(station => ({
      ...station,
      status: passedStationIds.includes(station.id) ? 'passed' : 'waiting'
    }))

    this.setData({ stations: updatedStations })
  },

  // 获取班车位置（通过第三方接口）
  async getBusLocation() {
    try {
      // 获取已过站点信息
      await this.getPassedStations()

      // 模拟获取实时位置（实际应该调用第三方GPS接口）
      const mockLocation = {
        lat: 24.5698 + (Math.random() - 0.5) * 0.01,
        lng: 118.1287 + (Math.random() - 0.5) * 0.01,
        address: '霞梧路口附近',
        speed: Math.floor(Math.random() * 60),
        updateTime: this.formatTime(new Date())
      }

      this.setData({
        busLocation: mockLocation,
        lastUpdateTime: this.formatTime(new Date())
      })

    } catch (error) {
      console.error('获取班车位置异常:', error)
      if (this.data.isLoading) {
        wx.showToast({
          title: '获取位置信息失败',
          icon: 'none'
        })
      }
    }
  },

  // 点击站点
  onStationTap(e) {
    const stationIndex = e.currentTarget.dataset.index
    const station = this.data.stations[stationIndex]

    const statusText = {
      'waiting': '未到达',
      'passed': '已通过',
      'current': '当前位置'
    }

    wx.showModal({
      title: station.name,
      content: `站点状态: ${statusText[station.status] || '未知'}`,
      showCancel: false
    })
  },

  // 开始自动刷新（5秒间隔）
  startAutoRefresh() {
    if (this.data.refreshTimer) {
      clearInterval(this.data.refreshTimer)
    }

    const timer = setInterval(() => {
      if (this.data.autoRefreshEnabled) {
        this.getBusLocation()
      }
    }, 5000) // 5秒刷新一次

    this.setData({ refreshTimer: timer })
    console.log('开始自动刷新，间隔5秒')
  },

  // 停止自动刷新
  stopAutoRefresh() {
    if (this.data.refreshTimer) {
      clearInterval(this.data.refreshTimer)
      this.setData({ refreshTimer: null })
      console.log('停止自动刷新')
    }
  },

  // 切换自动刷新状态
  toggleAutoRefresh() {
    const newState = !this.data.autoRefreshEnabled
    this.setData({ autoRefreshEnabled: newState })

    if (newState) {
      this.startAutoRefresh()
      wx.showToast({
        title: '已开启自动刷新',
        icon: 'success'
      })
    } else {
      this.stopAutoRefresh()
      wx.showToast({
        title: '已关闭自动刷新',
        icon: 'success'
      })
    }
  },

  // 手动刷新
  onRefresh() {
    this.getBusLocation()
    wx.showToast({
      title: '正在刷新...',
      icon: 'loading',
      duration: 1000
    })
  },

  // 格式化时间
  formatTime(date) {
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')
    const seconds = date.getSeconds().toString().padStart(2, '0')
    return `${hours}:${minutes}:${seconds}`
  },

  // 站点滑动
  onStationScroll(e) {
    // 可以在这里处理滑动逻辑
    console.log('滑动事件:', e)
  },

  // 点击站点
  onStationTap(e) {
    const stationIndex = e.currentTarget.dataset.index
    const station = this.data.stations[stationIndex]
    
    wx.showModal({
      title: station.name,
      content: `站点状态: ${this.getStationStatusText(station.status)}`,
      showCancel: false
    })
  },

  // 获取站点状态文本
  getStationStatusText(status) {
    const statusMap = {
      'waiting': '未到达',
      'current': '当前位置',
      'passed': '已通过'
    }
    return statusMap[status] || '未知'
  }
})
