// app.js
App({
  globalData: {
    userInfo: null,
    baseUrl: 'http://localhost:5000/api' // 本地测试接口
  },

  onLaunch() {
    // 简单初始化
    console.log('小程序启动')
  },

  // 获取用户信息（兼容旧版本调用）
  getUserInfo() {
    // 返回默认用户信息，实际获取在页面中进行
    const defaultUserInfo = {
      nickName: '用户',
      avatarUrl: '',
      phoneNumber: ''
    }
    this.globalData.userInfo = defaultUserInfo
    return defaultUserInfo
  },

  // 获取微信用户基本信息
  async getWeChatUserProfile() {
    try {
      // 检查API是否可用
      if (!wx.getUserProfile) {
        throw new Error('当前微信版本不支持getUserProfile')
      }

      const res = await new Promise((resolve, reject) => {
        wx.getUserProfile({
          desc: '用于完善用户资料',
          success: resolve,
          fail: reject
        })
      })

      if (res.userInfo) {
        console.log('获取用户信息成功:', res.userInfo)
        return res.userInfo
      } else {
        throw new Error('获取用户信息失败')
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      throw new Error('获取用户信息失败，请重试')
    }
  },

  // 获取用户openid
  async getOpenId() {
    try {
      const res = await new Promise((resolve, reject) => {
        wx.login({
          success: resolve,
          fail: reject
        })
      })

      if (res.code) {
        console.log('获取登录凭证成功:', res.code)
        // 调用后端接口获取openid
        const response = await this.request({
          url: '/user/login',
          method: 'POST',
          data: { code: res.code }
        })

        if (response.success && response.data && response.data.openid) {
          return response.data.openid
        } else {
          throw new Error(response.message || '获取OpenId失败')
        }
      } else {
        throw new Error('获取登录凭证失败')
      }
    } catch (error) {
      console.error('获取openid失败:', error)
      throw new Error('登录失败，请重试')
    }
  },

  // 检查用户是否已授权
  async checkUserAuth() {
    try {
      const openid = await this.getOpenId()
      const response = await this.request({
        url: '/user/info',
        method: 'GET',
        data: { openid }
      })

      if (response.success && response.data) {
        this.globalData.userInfo = response.data
        return response.data
      }
      return null
    } catch (error) {
      console.error('检查用户授权失败:', error)
      // 本地测试时返回null，表示需要授权
      return null
    }
  },

  // 用户授权并保存信息
  async saveUserAuth(userProfile, phoneNumber) {
    try {
      if (!userProfile || !phoneNumber) {
        throw new Error('用户信息不完整')
      }

      const openid = await this.getOpenId()
      const response = await this.request({
        url: '/user/auth',
        method: 'POST',
        data: {
          openid,
          nickName: userProfile.nickName,
          avatarUrl: userProfile.avatarUrl,
          phoneNumber
        }
      })

      if (response.success && response.data) {
        this.globalData.userInfo = response.data
        // 缓存用户信息
        wx.setStorageSync('userInfo', response.data)
        console.log('用户信息保存成功:', response.data)
        return response.data
      } else {
        throw new Error(response.message || '保存用户信息失败')
      }
    } catch (error) {
      console.error('保存用户授权失败:', error)

      // 本地测试时创建模拟用户信息
      if (error.message && error.message.includes('网络请求失败')) {
        console.log('网络请求失败，使用模拟数据')
        const mockUserInfo = {
          openId: 'mock_openid_' + Date.now(),
          nickName: userProfile.nickName,
          avatarUrl: userProfile.avatarUrl,
          phoneNumber: phoneNumber,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }

        this.globalData.userInfo = mockUserInfo
        wx.setStorageSync('userInfo', mockUserInfo)
        return mockUserInfo
      }

      throw error
    }
  },

  // 检查用户信息（兼容旧版本调用）
  checkUserInfo() {
    const cachedUserInfo = wx.getStorageSync('userInfo')
    if (cachedUserInfo) {
      this.globalData.userInfo = cachedUserInfo
      return true
    }
    return false
  },

  // 清除用户信息
  clearUserInfo() {
    this.globalData.userInfo = null
    wx.removeStorageSync('userInfo')
  },

  // 网络请求方法
  request(options) {
    return new Promise((resolve, reject) => {
      const requestOptions = {
        url: `${this.globalData.baseUrl}${options.url}`,
        method: options.method || 'GET',
        data: options.data || {},
        header: {
          'Content-Type': 'application/json',
          ...options.header
        },
        timeout: 10000, // 10秒超时
        success: (res) => {
          console.log(`请求成功: ${options.method || 'GET'} ${options.url}`, res.data)
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(res.data)
          } else {
            const error = new Error(`HTTP ${res.statusCode}: ${res.data?.message || 'Request failed'}`)
            error.statusCode = res.statusCode
            error.data = res.data
            reject(error)
          }
        },
        fail: (err) => {
          console.error(`请求失败: ${options.method || 'GET'} ${options.url}`, err)
          reject(new Error(err.errMsg || '网络请求失败'))
        }
      }

      wx.request(requestOptions)
    })
  }
})
