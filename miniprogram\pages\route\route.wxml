<!--pages/route/route.wxml-->
<view class="container">
  <!-- 班车信息卡片 -->
  <view class="bus-info-card">
    <view class="bus-header">
      <view class="bus-icon" style="background-color: {{routeInfo.color}}">
        <text class="icon-text">🚌</text>
      </view>
      <view class="bus-details">
        <text class="bus-name">{{routeInfo.name}}</text>
        <text class="bus-number">{{routeInfo.carNumber}}</text>
      </view>
      <view class="header-actions">
        <view class="auto-refresh-btn {{autoRefreshEnabled ? 'active' : ''}}" bindtap="toggleAutoRefresh">
          <text class="refresh-status">{{autoRefreshEnabled ? '自动' : '手动'}}</text>
        </view>
        <view class="refresh-btn" bindtap="onRefresh">
          <text class="refresh-icon">🔄</text>
        </view>
      </view>
    </view>

    <view class="bus-status" wx:if="{{busLocation}}">
      <view class="status-item">
        <text class="status-label">当前位置</text>
        <text class="status-value">{{busLocation.address}}</text>
      </view>
      <view class="status-item">
        <text class="status-label">最近站点</text>
        <text class="status-value">
          {{busLocation.nearestStationName}}
          <text class="distance-info">({{busLocation.distanceToStation}}m)</text>
        </text>
      </view>
      <view class="status-item">
        <text class="status-label">运行状态</text>
        <text class="status-value status-{{busLocation.isAtStation ? 'at-station' : 'moving'}}">
          {{busLocation.locationStatus}}
        </text>
      </view>
      <view class="status-item">
        <text class="status-label">行驶速度</text>
        <text class="status-value">{{busLocation.speed}} km/h</text>
      </view>
      <view class="status-item">
        <text class="status-label">更新时间</text>
        <text class="status-value">{{lastUpdateTime}}</text>
      </view>
    </view>

    <view class="loading" wx:if="{{isLoading}}">
      <text class="loading-text">正在获取班车位置...</text>
    </view>
  </view>

  <!-- 线路站点 -->
  <view class="route-section">
    <view class="section-header">
      <text class="section-title">线路站点</text>
      <text class="section-desc">左右滑动查看更多站点</text>
    </view>

    <scroll-view 
      class="stations-container" 
      scroll-x="true" 
      show-scrollbar="false"
      bindscroll="onStationScroll"
    >
      <view class="stations-wrapper">
        <!-- 连接线 -->
        <view class="connection-line"></view>
        
        <!-- 站点列表 -->
        <view 
          class="station-item {{item.status}}" 
          wx:for="{{stations}}" 
          wx:key="id"
          data-index="{{index}}"
          bindtap="onStationTap"
        >
          <view class="station-dot">
            <view class="dot-inner"></view>
            <!-- 班车图标（仅在当前站点显示） -->
            <view class="bus-marker" wx:if="{{item.status === 'current'}}">
              <text class="bus-icon-small">🚌</text>
            </view>
          </view>
          <view class="station-info">
            <text class="station-name">{{item.name}}</text>
            <text class="station-status">{{item.status === 'current' ? '班车在此' : (item.status === 'passed' ? '已通过' : '未到达')}}</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 操作提示 -->
  <view class="tips-section">
    <view class="tip-item">
      <text class="tip-icon">📍</text>
      <text class="tip-text">位置信息每5秒自动更新</text>
    </view>
    <view class="tip-item">
      <text class="tip-icon">👆</text>
      <text class="tip-text">点击站点查看详细信息</text>
    </view>
    <view class="tip-item">
      <text class="tip-icon">ℹ️</text>
      <text class="tip-text">30米内认为到站，可切换手动/自动刷新</text>
    </view>
  </view>
</view>
