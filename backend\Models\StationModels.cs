using System.ComponentModel.DataAnnotations;

namespace WeChatBus.Models
{
    /// <summary>
    /// 站点通过记录模型
    /// </summary>
    public class PassedStation
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int RouteId { get; set; }

        [Required]
        public int StationId { get; set; }

        [Required]
        [StringLength(20)]
        public string CarId { get; set; } = string.Empty;

        public DateTime PassedTime { get; set; } = DateTime.Now;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // 导航属性
        public BusRoute Route { get; set; } = null!;
        public BusStation Station { get; set; } = null!;
    }

    /// <summary>
    /// 更新已过站点请求模型
    /// </summary>
    public class UpdatePassedStationsRequest
    {
        [Required]
        public List<int> StationIds { get; set; } = new List<int>();

        public DateTime? PassedTime { get; set; }
    }

    /// <summary>
    /// 已过站点响应模型
    /// </summary>
    public class PassedStationResponse
    {
        public bool Success { get; set; }
        public List<PassedStationInfo>? Data { get; set; }
        public string? Message { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 已过站点信息
    /// </summary>
    public class PassedStationInfo
    {
        public int StationId { get; set; }
        public string StationName { get; set; } = string.Empty;
        public int Order { get; set; }
        public DateTime PassedTime { get; set; }
        public double Latitude { get; set; }
        public double Longitude { get; set; }
    }

    /// <summary>
    /// 用户信息存储请求模型
    /// </summary>
    public class UserInfoRequest
    {
        [Required]
        [StringLength(20)]
        public string PhoneNumber { get; set; } = string.Empty;

        [StringLength(100)]
        public string? NickName { get; set; }

        [StringLength(500)]
        public string? AvatarUrl { get; set; }

        [StringLength(100)]
        public string? OpenId { get; set; }

        [StringLength(100)]
        public string? UnionId { get; set; }

        /// <summary>
        /// 用户类型：employee-员工, visitor-访客
        /// </summary>
        [StringLength(20)]
        public string UserType { get; set; } = "visitor";

        /// <summary>
        /// 员工工号（如果是员工）
        /// </summary>
        [StringLength(50)]
        public string? EmployeeId { get; set; }

        /// <summary>
        /// 部门信息（如果是员工）
        /// </summary>
        [StringLength(100)]
        public string? Department { get; set; }
    }

    /// <summary>
    /// 用户信息响应模型
    /// </summary>
    public class UserInfoResponse
    {
        public bool Success { get; set; }
        public UserInfo? Data { get; set; }
        public string? Message { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 扩展的用户信息模型
    /// </summary>
    public class ExtendedUserInfo : UserInfo
    {
        public string? OpenId { get; set; }
        public string? UnionId { get; set; }
        public string UserType { get; set; } = "visitor";
        public string? EmployeeId { get; set; }
        public string? Department { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? LastLoginAt { get; set; }
    }

    /// <summary>
    /// 通用API响应模型
    /// </summary>
    public class ApiResponse<T>
    {
        public bool Success { get; set; }
        public T? Data { get; set; }
        public string? Message { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;
        public int? Code { get; set; }
    }

    /// <summary>
    /// 简化的线路信息（用于首页一次性加载）
    /// </summary>
    public class SimpleRouteInfo
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string CarId { get; set; } = string.Empty;
        public string CarNumber { get; set; } = string.Empty;
        public string Color { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public string Status { get; set; } = string.Empty;
        public string StatusText { get; set; } = string.Empty;
        public List<SimpleStationInfo> Stations { get; set; } = new List<SimpleStationInfo>();
        public List<int> PassedStationIds { get; set; } = new List<int>();
    }

    /// <summary>
    /// 简化的站点信息
    /// </summary>
    public class SimpleStationInfo
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public int Order { get; set; }
        public double Latitude { get; set; }
        public double Longitude { get; set; }
        public bool IsPassed { get; set; } = false;
    }
}
