// pages/index/index.js
const app = getApp()

Page({
  data: {
    routes: [],
    userInfo: null,
    isLoading: true,
    isInTimeRange: false, // 是否在可查询时间范围内
    needAuth: false, // 是否需要授权
    showAuthModal: false // 是否显示授权弹窗
  },

  onLoad() {
    // 初始化
    this.checkTimeRange()
    this.initUserInfo()
  },

  // 初始化用户信息
  async initUserInfo() {
    try {
      // 先尝试从缓存获取
      const cachedUserInfo = wx.getStorageSync('userInfo')
      if (cachedUserInfo) {
        this.setData({
          userInfo: cachedUserInfo,
          needAuth: false
        })
        this.loadDataIfNeeded()
        return
      }

      // 检查服务端是否有用户信息
      const userInfo = await app.checkUserAuth()
      if (userInfo) {
        this.setData({
          userInfo: userInfo,
          needAuth: false
        })
        wx.setStorageSync('userInfo', userInfo)
        this.loadDataIfNeeded()
      } else {
        // 需要用户授权
        this.setData({
          needAuth: true,
          isLoading: false
        })
      }
    } catch (error) {
      console.error('初始化用户信息失败:', error)
      this.setData({
        needAuth: true,
        isLoading: false
      })
    }
  },

  // 根据需要加载数据
  loadDataIfNeeded() {
    if (this.data.isInTimeRange) {
      this.loadRoutes()
    } else {
      this.setData({ isLoading: false })
    }
  },

  onShow() {
    // 刷新时间状态
    this.checkTimeRange()
    // 如果已授权且在时间范围内，刷新数据
    if (!this.data.needAuth && this.data.isInTimeRange) {
      this.loadRoutes()
    }
  },

  // 检查时间范围（6:00-9:00）
  checkTimeRange() {
    const now = new Date()
    const hour = now.getHours()
    const isInRange = hour >= 6 && hour < 9

    this.setData({
      isInTimeRange: isInRange
    })

    console.log(`当前时间: ${hour}:${now.getMinutes()}, 是否在查询范围内: ${isInRange}`)
  },

  // 显示授权弹窗
  async showAuthModal() {
    try {
      // 先获取用户基本信息
      const userProfile = await this.getUserProfile()
      this.tempUserProfile = userProfile

      // 显示授权弹窗
      this.setData({ showAuthModal: true })
    } catch (error) {
      console.error('获取用户基本信息失败:', error)
      wx.showToast({
        title: '需要授权基本信息',
        icon: 'none'
      })
    }
  },

  // 关闭授权弹窗
  closeAuthModal() {
    this.setData({ showAuthModal: false })
  },

  // 获取用户基本信息
  async getUserProfile() {
    try {
      const userProfile = await app.getWeChatUserProfile()
      this.tempUserProfile = userProfile
      return userProfile
    } catch (error) {
      wx.showToast({
        title: '需要授权才能使用',
        icon: 'none'
      })
      throw error
    }
  },

  // 获取用户手机号（微信原生API）
  async getPhoneNumber(e) {
    try {
      if (e.detail.errMsg !== 'getPhoneNumber:ok') {
        wx.showToast({
          title: '需要手机号授权',
          icon: 'none'
        })
        return
      }

      wx.showLoading({ title: '授权中...' })

      // 先获取用户基本信息
      let userProfile = this.tempUserProfile
      if (!userProfile) {
        try {
          userProfile = await this.getUserProfile()
        } catch (profileError) {
          wx.hideLoading()
          wx.showToast({
            title: '需要先授权基本信息',
            icon: 'none'
          })
          return
        }
      }

      // 直接使用微信返回的手机号
      const phoneNumber = e.detail.phoneNumber
      if (!phoneNumber) {
        wx.hideLoading()
        wx.showToast({
          title: '获取手机号失败',
          icon: 'none'
        })
        return
      }

      // 保存用户信息到服务端
      const userInfo = await app.saveUserAuth(userProfile, phoneNumber)

      this.setData({
        userInfo: userInfo,
        needAuth: false,
        showAuthModal: false
      })

      wx.hideLoading()
      wx.showToast({
        title: '授权成功',
        icon: 'success'
      })

      // 授权成功后加载数据
      this.loadDataIfNeeded()

    } catch (error) {
      wx.hideLoading()
      console.error('授权失败:', error)
      wx.showToast({
        title: error.message || '授权失败，请重试',
        icon: 'none'
      })
    }
  },

  // 重新授权
  async reAuth() {
    try {
      wx.showLoading({ title: '重新授权中...' })

      // 清除缓存
      app.clearUserInfo()

      // 获取新的用户信息
      const userProfile = await app.getWeChatUserProfile()
      this.tempUserProfile = userProfile

      // 显示授权弹窗
      this.setData({
        showAuthModal: true,
        needAuth: true
      })

      wx.hideLoading()
    } catch (error) {
      wx.hideLoading()
      wx.showToast({
        title: '重新授权失败',
        icon: 'none'
      })
    }
  },


  // 简单加载数据（本地测试用模拟数据）
  async loadRoutes() {
    if (!this.data.isInTimeRange) {
      this.setData({ isLoading: false })
      return
    }

    this.setData({ isLoading: true })

    try {
      // 本地测试：使用模拟数据
      const mockData = [
        {
          id: 1,
          name: '1号线',
          carNumber: '厦A12345',
          color: '#4ECDC4',
          isActive: true,
          status: 'available',
          statusText: '运行中',
          stations: [
            { id: 1, name: '软件园站', order: 1, isPassed: true },
            { id: 2, name: '观音山站', order: 2, isPassed: false },
            { id: 3, name: '厦大站', order: 3, isPassed: false }
          ]
        },
        {
          id: 2,
          name: '2号线',
          carNumber: '厦A67890',
          color: '#FF6B6B',
          isActive: true,
          status: 'available',
          statusText: '运行中',
          stations: [
            { id: 4, name: '火车站', order: 1, isPassed: false },
            { id: 5, name: '中山路', order: 2, isPassed: false },
            { id: 6, name: '轮渡', order: 3, isPassed: false }
          ]
        }
      ]

      // 模拟网络延迟
      setTimeout(() => {
        this.setData({
          routes: mockData,
          isLoading: false
        })
      }, 500)

    } catch (error) {
      console.error('加载数据失败:', error)
      this.setData({ isLoading: false })
    }
  },

  // 简单刷新
  refreshData() {
    this.checkTimeRange()
    if (this.data.isInTimeRange) {
      this.loadRoutes()
    }
  },

  // 点击线路卡片
  onRouteClick(e) {
    const route = e.currentTarget.dataset.route



    if (route.isActive) {
      // 跳转到线路详情页，传递完整的线路数据
      const routeData = encodeURIComponent(JSON.stringify(route))
      wx.navigateTo({
        url: `/pages/route/route?routeData=${routeData}`
      })
    } else {
      // 显示即将接入提示
      wx.showModal({
        title: '提示',
        content: route.statusText || '正在接入中，请稍后...',
        showCancel: false,
        confirmText: '知道了'
      })
    }
  },




  // 下拉刷新
  onPullDownRefresh() {
    this.refreshData()
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },


})
