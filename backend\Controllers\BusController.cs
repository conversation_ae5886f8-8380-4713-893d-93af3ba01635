using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WeChatBus.Models;
using WeChatBus.Services;

namespace WeChatBus.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class BusController : ControllerBase
    {
        private readonly IBusLocationService _busLocationService;
        private readonly ILogger<BusController> _logger;

        public BusController(IBusLocationService busLocationService, ILogger<BusController> logger)
        {
            _busLocationService = busLocationService;
            _logger = logger;
        }

        /// <summary>
        /// 获取所有班车线路（专用于小程序首页展示）
        /// </summary>
        [HttpGet("routes")]
        public async Task<ActionResult<object>> GetRoutes()
        {
            try
            {
                var routes = await _busLocationService.GetAllRoutesAsync();

                var result = routes.Select(r => new
                {
                    Id = r.Id,
                    Name = r.Name,
                    CarId = r.CarId,
                    CarNumber = r.CarNumber,
                    Color = r.Color,
                    Status = r.IsActive ? "available" : "coming",
                    StatusText = r.IsActive ? "运行中" : "正在接入中，请稍后...",
                    Icon = $"/images/route{r.Id}.png"
                }).OrderBy(r => r.Id).ToList();

                return Ok(new
                {
                    Success = true,
                    Data = result,
                    Message = "获取线路列表成功",
                    Timestamp = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取线路列表失败");
                return StatusCode(500, new
                {
                    Success = false,
                    Message = "获取线路信息失败"
                });
            }
        }

        /// <summary>
        /// 获取指定线路信息
        /// </summary>
        [HttpGet("route/{routeId}")]
        public async Task<ActionResult<RouteInfoResponse>> GetRoute(int routeId)
        {
            try
            {
                var result = await _busLocationService.GetRouteInfoAsync(routeId);
                
                if (result.Success)
                {
                    return Ok(result);
                }
                else
                {
                    return NotFound(result);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取线路信息失败: {routeId}");
                return StatusCode(500, new RouteInfoResponse
                {
                    Success = false,
                    Message = "获取线路信息失败"
                });
            }
        }

        /// <summary>
        /// 获取班车实时位置
        /// </summary>
        [HttpGet("location/{carId}")]
        public async Task<ActionResult<BusLocationResponse>> GetBusLocation(string carId)
        {
            try
            {
                if (string.IsNullOrEmpty(carId))
                {
                    return BadRequest(new BusLocationResponse
                    {
                        Success = false,
                        Message = "车辆ID不能为空"
                    });
                }

                var result = await _busLocationService.GetBusLocationAsync(carId);
                
                if (result.Success)
                {
                    _logger.LogInformation($"获取车辆位置成功: {carId}");
                    return Ok(result);
                }
                else
                {
                    _logger.LogWarning($"获取车辆位置失败: {carId}, 原因: {result.Message}");
                    return NotFound(result);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取车辆位置接口异常: {carId}");
                return StatusCode(500, new BusLocationResponse
                {
                    Success = false,
                    Message = "服务器内部错误"
                });
            }
        }

        /// <summary>
        /// 批量获取多个车辆位置
        /// </summary>
        [HttpPost("locations")]
        public async Task<ActionResult<object>> GetBusLocations([FromBody] string[] carIds)
        {
            try
            {
                if (carIds == null || !carIds.Any())
                {
                    return BadRequest(new
                    {
                        Success = false,
                        Message = "车辆ID列表不能为空"
                    });
                }

                var tasks = carIds.Select(carId => _busLocationService.GetBusLocationAsync(carId));
                var results = await Task.WhenAll(tasks);

                var successResults = results
                    .Where(r => r.Success)
                    .Select(r => r.Data)
                    .ToList();

                return Ok(new
                {
                    Success = true,
                    Data = successResults,
                    Total = successResults.Count
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量获取车辆位置失败");
                return StatusCode(500, new
                {
                    Success = false,
                    Message = "获取车辆位置失败"
                });
            }
        }

        /// <summary>
        /// 健康检查接口
        /// </summary>
        [HttpGet("health")]
        [AllowAnonymous]
        public ActionResult<object> Health()
        {
            return Ok(new
            {
                Success = true,
                Message = "班车服务运行正常",
                Timestamp = DateTime.Now
            });
        }
    }
}
