using System.ComponentModel.DataAnnotations;

namespace WeChatBus.Models
{
    public class User
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string OpenId { get; set; } = string.Empty;

        [Required]
        [StringLength(20)]
        public string PhoneNumber { get; set; } = string.Empty;

        [StringLength(100)]
        public string? NickName { get; set; }

        [StringLength(500)]
        public string? AvatarUrl { get; set; }

        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 用户类型：employee-员工, visitor-访客
        /// </summary>
        [StringLength(20)]
        public string UserType { get; set; } = "visitor";

        /// <summary>
        /// 员工工号（如果是员工）
        /// </summary>
        [StringLength(50)]
        public string? EmployeeId { get; set; }

        /// <summary>
        /// 部门信息（如果是员工）
        /// </summary>
        [StringLength(100)]
        public string? Department { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime? LastLoginAt { get; set; }

        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// 用户授权请求模型
    /// </summary>
    public class UserAuthRequest
    {
        [Required]
        public string OpenId { get; set; } = string.Empty;

        [Required]
        public string PhoneNumber { get; set; } = string.Empty;

        [Required]
        public string NickName { get; set; } = string.Empty;

        public string AvatarUrl { get; set; } = string.Empty;
    }

    /// <summary>
    /// 微信登录请求模型
    /// </summary>
    public class WeChatLoginRequest
    {
        [Required]
        public string Code { get; set; } = string.Empty;
    }

    /// <summary>
    /// 用户信息响应模型
    /// </summary>
    public class UserInfoResponse
    {
        public string OpenId { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
        public string NickName { get; set; } = string.Empty;
        public string AvatarUrl { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }
}
