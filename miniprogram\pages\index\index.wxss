/* pages/index/index.wxss - 简约大气版本 */
.container {
  min-height: 100vh;
  background: #f5f5f5;
}

/* 简洁头部 */
.header {
  background: #fff;
  padding: 40rpx 30rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.user-info {
  text-align: right;
}

.user-name {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 5rpx;
}

.phone {
  font-size: 24rpx;
  color: #666;
}

.reauth-btn {
  font-size: 22rpx;
  color: #4CAF50;
  margin-top: 5rpx;
  padding: 5rpx 10rpx;
  border: 1rpx solid #4CAF50;
  border-radius: 10rpx;
}

.auth-info {
  text-align: right;
}

.auth-text {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 5rpx;
}

.auth-btn {
  font-size: 22rpx;
  color: #4CAF50;
  padding: 5rpx 10rpx;
  border: 1rpx solid #4CAF50;
  border-radius: 10rpx;
}

/* 时间状态 */
.time-status {
  background: #fff;
  margin: 20rpx 30rpx;
  padding: 25rpx 30rpx;
  border-radius: 15rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.time-text {
  font-size: 28rpx;
  color: #333;
}

.status-dot {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
}

.status-dot.active {
  background: #4CAF50;
}

.status-dot.inactive {
  background: #FF5722;
}

/* 内容区域 */
.content {
  padding: 0 30rpx;
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 100rpx 0;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 时间提示 */
.time-notice {
  background: #fff;
  border-radius: 15rpx;
  padding: 80rpx 40rpx;
  text-align: center;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.notice-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.notice-text {
  font-size: 32rpx;
  color: #666;
}

/* 班车列表 */
.bus-list {
  background: #fff;
  border-radius: 15rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.bus-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.bus-item:last-child {
  border-bottom: none;
}

.bus-info {
  flex: 1;
}

.bus-name {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.bus-car {
  font-size: 26rpx;
  color: #666;
}

.bus-status {
  display: flex;
  align-items: center;
}

.bus-status .status-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: #4CAF50;
  margin-right: 10rpx;
}

.status-text {
  font-size: 26rpx;
  color: #4CAF50;
}

/* 授权提示 */
.auth-notice {
  background: #fff;
  border-radius: 15rpx;
  padding: 80rpx 40rpx;
  text-align: center;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.notice-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.notice-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 30rpx;
  line-height: 1.5;
}

.auth-button {
  background: #4CAF50;
  color: #fff;
  border: none;
  border-radius: 25rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

.auth-button::after {
  border: none;
}

/* 授权弹窗 */
.auth-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 600rpx;
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: 40rpx 30rpx;
  text-align: center;
}

.auth-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.auth-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  border: none;
  border-radius: 0;
}

.cancel-btn {
  background: #f8f9fa;
  color: #666;
  border-right: 1rpx solid #f0f0f0;
}

.confirm-btn {
  background: #4CAF50;
  color: #fff;
}

.cancel-btn::after,
.confirm-btn::after {
  border: none;
}
