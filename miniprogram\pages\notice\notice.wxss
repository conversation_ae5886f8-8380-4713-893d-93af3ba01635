/* pages/notice/notice.wxss */
.container {
  min-height: 100vh;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.notice-content {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 80rpx 60rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  max-width: 600rpx;
}

.icon-container {
  margin-bottom: 40rpx;
}

.notice-icon {
  font-size: 120rpx;
  display: block;
}

.notice-text {
  margin-bottom: 40rpx;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
}

.subtitle {
  display: block;
  font-size: 36rpx;
  color: #4ECDC4;
  font-weight: 500;
}

.description {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.desc-text {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
  margin-bottom: 10rpx;
}