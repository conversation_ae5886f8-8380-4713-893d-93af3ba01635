# 微信小程序班车查看系统 - 架构调整部署指南

## 架构调整概览

本次架构调整主要实现了以下优化：

### 1. 后端架构调整
- ✅ **新增站点管理接口**: 支持已过站点的更新和查询
- ✅ **移除认证相关代码**: 简化架构，通过nginx代理第三方接口
- ✅ **统一API前缀**: `/api/kwange/` 用于我们的接口，`/api/qiye/` 用于第三方代理
- ✅ **扩展用户模型**: 支持员工信息和权限管理预留

### 2. 前端扁平化设计
- ✅ **一次性数据加载**: 首次打开获取所有必要信息并缓存
- ✅ **减少用户操作**: 简化授权流程，按需引导用户授权
- ✅ **优化交互体验**: 减少页面跳转和加载状态

### 3. Nginx代理配置
- ✅ **统一入口**: 通过nginx代理分发不同类型的API请求
- ✅ **第三方接口代理**: 解决跨域问题，统一管理外部依赖
- ✅ **负载均衡**: 支持多实例部署和故障转移

## 新增API接口

### 站点管理接口

#### 1. 更新已过站点
```http
POST /api/kwange/route/{routeId}/passed-stations
Content-Type: application/json

{
  "stationIds": [1, 2, 3],
  "passedTime": "2025-01-23T10:30:00Z"
}
```

**响应**:
```json
{
  "success": true,
  "data": true,
  "message": "更新成功",
  "timestamp": "2025-01-23T10:30:00Z"
}
```

#### 2. 获取已过站点
```http
GET /api/kwange/route/{routeId}/passed-stations
```

**响应**:
```json
{
  "success": true,
  "data": [
    {
      "stationId": 1,
      "stationName": "嘉庚体育馆",
      "order": 1,
      "passedTime": "2025-01-23T10:15:00Z",
      "latitude": 24.5804,
      "longitude": 118.0965
    }
  ],
  "message": "获取成功",
  "timestamp": "2025-01-23T10:30:00Z"
}
```

### 用户信息接口

#### 3. 保存用户信息
```http
POST /api/kwange/user/info
Content-Type: application/json

{
  "phoneNumber": "13800138000",
  "nickName": "张三",
  "avatarUrl": "https://example.com/avatar.jpg",
  "userType": "visitor",
  "employeeId": "EMP001",
  "department": "技术部"
}
```

#### 4. 获取线路信息（扁平化）
```http
GET /api/kwange/routes
```

**响应**:
```json
{
  "success": true,
  "data": [
    {
      "id": 2,
      "name": "2号线",
      "carId": "1162",
      "carNumber": "闽DZ5829",
      "color": "#4ECDC4",
      "isActive": true,
      "status": "available",
      "statusText": "运行中",
      "passedStationIds": [1, 2],
      "stations": [
        {
          "id": 1,
          "name": "嘉庚体育馆",
          "order": 1,
          "latitude": 24.5804,
          "longitude": 118.0965,
          "isPassed": true
        }
      ]
    }
  ],
  "message": "获取成功",
  "timestamp": "2025-01-23T10:30:00Z"
}
```

## 部署步骤

### 1. 数据库更新

#### 执行数据库迁移
```bash
# 使用更新后的SQL脚本
mysql -u root -p < backend/Scripts/mysql_init.sql

# 或者使用EF Core迁移
cd backend
dotnet ef migrations add ArchitectureUpdate
dotnet ef database update
```

#### 验证新表结构
```sql
-- 检查新增的表和字段
DESCRIBE Users;
DESCRIBE PassedStations;

-- 验证数据
SELECT COUNT(*) FROM BusRoutes;
SELECT COUNT(*) FROM BusStations;
SELECT COUNT(*) FROM PassedStations;
```

### 2. 后端部署

#### 更新依赖和配置
```bash
cd backend

# 恢复依赖
dotnet restore

# 构建项目
dotnet build

# 运行测试（如果有）
dotnet test

# 发布生产版本
dotnet publish -c Release -o ./publish
```

#### 配置文件更新
确保 `appsettings.json` 包含正确的配置：

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Port=3306;Database=WeChatBusDb;Uid=root;Pwd=your_password;CharSet=utf8mb4;"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  }
}
```

### 3. Nginx配置部署

#### 安装和配置Nginx
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install nginx

# CentOS/RHEL
sudo yum install nginx
# 或者
sudo dnf install nginx

# 复制配置文件
sudo cp nginx/wechatbus.conf /etc/nginx/sites-available/
sudo ln -s /etc/nginx/sites-available/wechatbus.conf /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
sudo systemctl enable nginx
```

#### SSL证书配置
```bash
# 使用Let's Encrypt（推荐）
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com

# 或者手动配置SSL证书
# 将证书文件放置到指定位置并更新nginx配置中的路径
```

### 4. 小程序配置更新

#### 更新API地址
编辑 `miniprogram/app.js`:

```javascript
globalData: {
  baseUrl: 'https://your-domain.com/api',
  kwangeApiUrl: 'https://your-domain.com/api/kwange',
  qiyeApiUrl: 'https://your-domain.com/api/qiye'
}
```

#### 微信公众平台配置
在微信公众平台配置服务器域名白名单：
- **request合法域名**: `https://your-domain.com`
- **socket合法域名**: `https://your-domain.com`（如需要）

### 5. 系统服务配置

#### 创建systemd服务文件
```bash
sudo nano /etc/systemd/system/wechatbus.service
```

```ini
[Unit]
Description=WeChatBus .NET Core App
After=network.target

[Service]
Type=notify
WorkingDirectory=/var/www/wechatbus
ExecStart=/usr/bin/dotnet /var/www/wechatbus/WeChatBus.dll
Restart=always
RestartSec=10
KillSignal=SIGINT
SyslogIdentifier=wechatbus
User=www-data
Environment=ASPNETCORE_ENVIRONMENT=Production
Environment=ASPNETCORE_URLS=http://localhost:5000

[Install]
WantedBy=multi-user.target
```

#### 启动服务
```bash
sudo systemctl daemon-reload
sudo systemctl enable wechatbus
sudo systemctl start wechatbus
sudo systemctl status wechatbus
```

## 测试验证

### 1. 后端API测试
```bash
# 健康检查
curl https://your-domain.com/api/kwange/health

# 获取线路信息
curl https://your-domain.com/api/kwange/routes

# 测试站点管理接口
curl -X POST https://your-domain.com/api/kwange/route/2/passed-stations \
  -H "Content-Type: application/json" \
  -d '{"stationIds": [1, 2]}'
```

### 2. Nginx代理测试
```bash
# 测试我们的接口代理
curl https://your-domain.com/api/kwange/health

# 测试第三方接口代理
curl https://your-domain.com/api/qiye/login
```

### 3. 小程序功能测试
- ✅ 首页线路列表加载
- ✅ 用户授权流程
- ✅ 线路详情页展示
- ✅ 数据缓存机制
- ✅ 网络异常处理

## 监控和维护

### 1. 日志监控
```bash
# 查看应用日志
sudo journalctl -u wechatbus -f

# 查看Nginx日志
sudo tail -f /var/log/nginx/wechatbus_access.log
sudo tail -f /var/log/nginx/wechatbus_error.log

# 查看系统资源使用
htop
df -h
```

### 2. 性能监控
- **应用性能**: 使用Application Insights或自定义监控
- **数据库性能**: 监控MySQL慢查询和连接数
- **Nginx性能**: 监控请求响应时间和错误率

### 3. 备份策略
```bash
# 数据库备份
mysqldump -u root -p WeChatBusDb > backup_$(date +%Y%m%d_%H%M%S).sql

# 应用程序备份
tar -czf app_backup_$(date +%Y%m%d_%H%M%S).tar.gz /var/www/wechatbus

# 配置文件备份
cp /etc/nginx/sites-available/wechatbus.conf nginx_backup_$(date +%Y%m%d_%H%M%S).conf
```

## 故障排除

### 常见问题

#### 1. 数据库连接失败
```bash
# 检查MySQL服务状态
sudo systemctl status mysql

# 测试数据库连接
mysql -u root -p -e "SELECT VERSION();"

# 检查防火墙设置
sudo ufw status
```

#### 2. Nginx代理失败
```bash
# 检查Nginx配置
sudo nginx -t

# 检查上游服务器状态
curl http://127.0.0.1:5000/api/kwange/health

# 查看错误日志
sudo tail -f /var/log/nginx/error.log
```

#### 3. 小程序接口调用失败
- 检查域名白名单配置
- 验证SSL证书有效性
- 确认API接口路径正确

### 性能优化建议

1. **数据库优化**
   - 添加适当的索引
   - 定期清理过期数据
   - 配置连接池参数

2. **缓存策略**
   - 使用Redis缓存热点数据
   - 配置HTTP缓存头
   - 实现应用级缓存

3. **负载均衡**
   - 部署多个后端实例
   - 配置Nginx负载均衡
   - 实现健康检查

本架构调整完成后，系统将具备更好的扩展性、性能和用户体验。
