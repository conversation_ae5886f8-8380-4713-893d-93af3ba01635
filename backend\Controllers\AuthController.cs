using Microsoft.AspNetCore.Mvc;
using WeChatBus.Models;
using WeChatBus.Services;

namespace WeChatBus.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerBase
    {
        private readonly IAuthService _authService;
        private readonly ILogger<AuthController> _logger;

        public AuthController(IAuthService authService, ILogger<AuthController> logger)
        {
            _authService = authService;
            _logger = logger;
        }

        /// <summary>
        /// 用户登录
        /// </summary>
        [HttpPost("login")]
        public async Task<ActionResult<LoginResponse>> Login([FromBody] LoginRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.PhoneNumber))
                {
                    return BadRequest(new LoginResponse
                    {
                        Success = false,
                        Message = "手机号不能为空"
                    });
                }

                var result = await _authService.LoginAsync(request.PhoneNumber);
                
                if (result.Success)
                {
                    _logger.LogInformation($"用户登录成功: {request.PhoneNumber}");
                    return Ok(result);
                }
                else
                {
                    _logger.LogWarning($"用户登录失败: {request.PhoneNumber}, 原因: {result.Message}");
                    return BadRequest(result);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"登录接口异常: {request.PhoneNumber}");
                return StatusCode(500, new LoginResponse
                {
                    Success = false,
                    Message = "服务器内部错误"
                });
            }
        }

        /// <summary>
        /// 解密微信手机号
        /// </summary>
        [HttpPost("decrypt-phone")]
        public async Task<ActionResult<DecryptPhoneResponse>> DecryptPhone([FromBody] DecryptPhoneRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.Code) || 
                    string.IsNullOrEmpty(request.EncryptedData) || 
                    string.IsNullOrEmpty(request.Iv))
                {
                    return BadRequest(new DecryptPhoneResponse
                    {
                        Success = false,
                        Message = "参数不完整"
                    });
                }

                var result = await _authService.DecryptPhoneNumberAsync(request.Code, request.EncryptedData, request.Iv);
                
                if (result.Success)
                {
                    _logger.LogInformation($"手机号解密成功: {result.PhoneNumber}");
                    return Ok(result);
                }
                else
                {
                    _logger.LogWarning($"手机号解密失败: {result.Message}");
                    return BadRequest(result);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "解密手机号接口异常");
                return StatusCode(500, new DecryptPhoneResponse
                {
                    Success = false,
                    Message = "服务器内部错误"
                });
            }
        }

        /// <summary>
        /// 测试接口 - 验证JWT token
        /// </summary>
        [HttpGet("test")]
        [Microsoft.AspNetCore.Authorization.Authorize]
        public ActionResult<object> Test()
        {
            var userId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
            var phoneNumber = User.FindFirst("phone")?.Value;
            
            return Ok(new
            {
                Success = true,
                Message = "Token验证成功",
                Data = new
                {
                    UserId = userId,
                    PhoneNumber = phoneNumber,
                    Claims = User.Claims.Select(c => new { c.Type, c.Value }).ToList()
                }
            });
        }
    }
}
