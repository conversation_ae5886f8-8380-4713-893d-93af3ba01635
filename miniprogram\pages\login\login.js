// pages/login/login.js
const app = getApp()

Page({
  data: {
    canIUseGetUserProfile: wx.canIUse('getUserProfile'),
    canIUseNicknameComp: wx.canIUse('input.type.nickname'),
  },

  onLoad() {
    // 设置页面标题
    wx.setNavigationBarTitle({
      title: '厦华科技'
    })

    // 检查是否已经授权
    if (app.checkUserInfo()) {
      wx.switchTab({
        url: '/pages/index/index'
      })
    }
  },

  // 获取用户手机号
  getPhoneNumber(e) {
    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      // 显示加载提示
      wx.showLoading({
        title: '授权中...'
      })

      // 获取到加密数据，需要发送到后端解密
      const { encryptedData, iv } = e.detail

      // 先获取用户信息
      this.getUserProfile().then(() => {
        this.decryptPhoneNumber(encryptedData, iv)
      }).catch(() => {
        wx.hideLoading()
        wx.showToast({
          title: '获取用户信息失败',
          icon: 'none'
        })
      })
    } else {
      wx.showToast({
        title: '需要授权才能使用班车查询服务',
        icon: 'none',
        duration: 3000
      })
    }
  },

  // 获取用户基本信息
  getUserProfile() {
    return new Promise((resolve, reject) => {
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => {
          app.globalData.userInfo = res.userInfo
          resolve(res)
        },
        fail: (err) => {
          wx.showToast({
            title: '需要用户信息授权',
            icon: 'none'
          })
          reject(err)
        }
      })
    })
  },

  // 解密手机号
  decryptPhoneNumber(encryptedData, iv) {
    wx.login({
      success: (loginRes) => {
        if (loginRes.code) {
          // 发送到后端解密手机号
          wx.request({
            url: `${app.globalData.baseUrl}/auth/decrypt-phone`,
            method: 'POST',
            data: {
              code: loginRes.code,
              encryptedData: encryptedData,
              iv: iv
            },
            success: (res) => {
              if (res.data.success) {
                const phoneNumber = res.data.phoneNumber
                
                // 调用登录接口
                app.login(phoneNumber, (success, message) => {
                  if (success) {
                    wx.hideLoading()
                    wx.showToast({
                      title: '授权成功',
                      icon: 'success'
                    })

                    setTimeout(() => {
                      wx.switchTab({
                        url: '/pages/index/index'
                      })
                    }, 1500)
                  } else {
                    wx.showToast({
                      title: message || '登录失败',
                      icon: 'none'
                    })
                  }
                })
              } else {
                wx.showToast({
                  title: '手机号获取失败',
                  icon: 'none'
                })
              }
            },
            fail: (err) => {
              console.error('解密手机号失败:', err)
              wx.showToast({
                title: '网络错误',
                icon: 'none'
              })
            }
          })
        } else {
          wx.showToast({
            title: '登录失败',
            icon: 'none'
          })
        }
      }
    })
  }
})
