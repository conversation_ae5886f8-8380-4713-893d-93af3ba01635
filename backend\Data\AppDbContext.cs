using Microsoft.EntityFrameworkCore;
using WeChatBus.Models;

namespace WeChatBus.Data
{
    public class AppDbContext : DbContext
    {
        public AppDbContext(DbContextOptions<AppDbContext> options) : base(options)
        {
        }

        public DbSet<User> Users { get; set; }
        public DbSet<BusRoute> BusRoutes { get; set; }
        public DbSet<BusStation> BusStations { get; set; }
        public DbSet<PassedStation> PassedStations { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // 配置用户表
            modelBuilder.Entity<User>(entity =>
            {
                entity.HasIndex(e => e.OpenId).IsUnique();
                entity.HasIndex(e => e.PhoneNumber).IsUnique();

                entity.Property(e => e.OpenId).IsRequired().HasMaxLength(100);
                entity.Property(e => e.PhoneNumber).IsRequired().HasMaxLength(20);
                entity.Property(e => e.NickName).HasMaxLength(100);
                entity.Property(e => e.AvatarUrl).HasMaxLength(500);
                entity.Property(e => e.UserType).HasMaxLength(20).HasDefaultValue("visitor");
                entity.Property(e => e.EmployeeId).HasMaxLength(50);
                entity.Property(e => e.Department).HasMaxLength(100);
                entity.Property(e => e.CreatedAt).HasColumnType("datetime(6)");
                entity.Property(e => e.UpdatedAt).HasColumnType("datetime(6)");
                entity.Property(e => e.LastLoginAt).HasColumnType("datetime(6)");

                // 添加索引
                entity.HasIndex(e => e.EmployeeId);
                entity.HasIndex(e => e.UserType);
            });

            // 配置班车路线表
            modelBuilder.Entity<BusRoute>(entity =>
            {
                entity.HasIndex(e => e.CarId).IsUnique();
                entity.Property(e => e.Name).IsRequired().HasMaxLength(50);
                entity.Property(e => e.CarId).IsRequired().HasMaxLength(20);
                entity.Property(e => e.CarNumber).IsRequired().HasMaxLength(20);
                entity.Property(e => e.Color).HasMaxLength(20);
                entity.Property(e => e.CreatedAt).HasColumnType("datetime(6)");
            });

            // 配置班车站点表
            modelBuilder.Entity<BusStation>(entity =>
            {
                entity.HasOne(d => d.Route)
                    .WithMany(p => p.Stations)
                    .HasForeignKey(d => d.RouteId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(e => new { e.RouteId, e.Order }).IsUnique();
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Latitude).HasColumnType("double");
                entity.Property(e => e.Longitude).HasColumnType("double");
            });

            // 配置已过站点表
            modelBuilder.Entity<PassedStation>(entity =>
            {
                entity.HasOne(d => d.Route)
                    .WithMany()
                    .HasForeignKey(d => d.RouteId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(d => d.Station)
                    .WithMany()
                    .HasForeignKey(d => d.StationId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.Property(e => e.CarId).IsRequired().HasMaxLength(20);
                entity.Property(e => e.PassedTime).HasColumnType("datetime(6)");
                entity.Property(e => e.CreatedAt).HasColumnType("datetime(6)");

                // 添加索引
                entity.HasIndex(e => new { e.RouteId, e.CarId });
                entity.HasIndex(e => e.StationId);
                entity.HasIndex(e => e.PassedTime);
            });

            // 初始化数据
            SeedData(modelBuilder);
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // 初始化班车路线数据
            modelBuilder.Entity<BusRoute>().HasData(
                new BusRoute { Id = 1, Name = "1号线", CarId = "1181", CarNumber = "闽DY1576", Color = "#FF6B6B", IsActive = false },
                new BusRoute { Id = 2, Name = "2号线", CarId = "1162", CarNumber = "闽DZ5829", Color = "#4ECDC4", IsActive = true },
                new BusRoute { Id = 3, Name = "3号线", CarId = "1171", CarNumber = "闽DX1686", Color = "#45B7D1", IsActive = false },
                new BusRoute { Id = 4, Name = "4号线", CarId = "5699", CarNumber = "闽DX3180", Color = "#96CEB4", IsActive = false },
                new BusRoute { Id = 5, Name = "5号线", CarId = "1168", CarNumber = "闽DZ9581", Color = "#FFEAA7", IsActive = false }
            );

            // 初始化2号线站点数据
            modelBuilder.Entity<BusStation>().HasData(
                new BusStation { Id = 1, RouteId = 2, Name = "嘉庚体育馆", Order = 1, Latitude = 24.5804, Longitude = 118.0965 },
                new BusStation { Id = 2, RouteId = 2, Name = "集美厂区", Order = 2, Latitude = 24.5756, Longitude = 118.1123 },
                new BusStation { Id = 3, RouteId = 2, Name = "霞梧路口", Order = 3, Latitude = 24.5698, Longitude = 118.1287 },
                new BusStation { Id = 4, RouteId = 2, Name = "叶厝", Order = 4, Latitude = 24.5634, Longitude = 118.1456 },
                new BusStation { Id = 5, RouteId = 2, Name = "禹州大学城", Order = 5, Latitude = 24.5589, Longitude = 118.1623 },
                new BusStation { Id = 6, RouteId = 2, Name = "洪塘头", Order = 6, Latitude = 24.5523, Longitude = 118.1789 },
                new BusStation { Id = 7, RouteId = 2, Name = "酱文化园", Order = 7, Latitude = 24.5467, Longitude = 118.1954 },
                new BusStation { Id = 8, RouteId = 2, Name = "翔安厂区", Order = 8, Latitude = 24.5412, Longitude = 118.2123 }
            );
        }
    }
}
